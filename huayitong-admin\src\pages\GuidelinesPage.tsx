import React, { useState } from 'react';
import { 
  BookOpen, 
  Search, 
  Filter,
  Download,
  Star,
  Calendar,
  Tag,
  Award,
  TrendingUp,
  FileText,
  ExternalLink
} from 'lucide-react';
import { mockGuidelines } from '../data/mockData';

const GuidelinesPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedEvidence, setSelectedEvidence] = useState<string>('all');

  const categories = [
    { value: 'all', label: '全部' },
    { value: '心血管', label: '心血管' },
    { value: '内分泌', label: '内分泌' },
    { value: '呼吸科', label: '呼吸科' },
    { value: '肾内科', label: '肾内科' }
  ];

  const evidenceLevels = [
    { value: 'all', label: '全部证据等级' },
    { value: 'A', label: 'A级证据' },
    { value: 'B', label: 'B级证据' },
    { value: 'C', label: 'C级证据' }
  ];

  const filteredGuidelines = mockGuidelines.filter(guideline => {
    const matchesSearch = guideline.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         guideline.organization.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || guideline.category === selectedCategory;
    const matchesEvidence = selectedEvidence === 'all' || guideline.evidenceLevel === selectedEvidence;
    return matchesSearch && matchesCategory && matchesEvidence;
  });

  const getEvidenceConfig = (level: string) => {
    switch (level) {
      case 'A':
        return { label: 'A级', color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' };
      case 'B':
        return { label: 'B级', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' };
      case 'C':
        return { label: 'C级', color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200' };
      default:
        return { label: '未知', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  return (
    <div className="h-full p-6 overflow-y-auto">
      {/* 顶部标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 flex items-center mb-4">
          <BookOpen size={28} className="mr-3 text-primary" />
          指南共识
        </h1>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">收录指南</p>
                <p className="text-2xl font-bold text-primary">156</p>
              </div>
              <BookOpen size={24} className="text-primary" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">最新更新</p>
                <p className="text-2xl font-bold text-green-600">12</p>
              </div>
              <TrendingUp size={24} className="text-green-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">我的收藏</p>
                <p className="text-2xl font-bold text-yellow-600">24</p>
              </div>
              <Star size={24} className="text-yellow-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">本月阅读</p>
                <p className="text-2xl font-bold text-blue-600">89</p>
              </div>
              <Award size={24} className="text-blue-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="glass-card rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索指南名称或机构"
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            />
          </div>

          {/* 分类筛选 */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>

          {/* 证据等级筛选 */}
          <select
            value={selectedEvidence}
            onChange={(e) => setSelectedEvidence(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
          >
            {evidenceLevels.map((level) => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* 推荐指南 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">🔥 热门推荐</h2>
        
        <div className="glass-card rounded-lg p-6 bg-gradient-to-r from-primary-50 to-blue-50 border border-primary-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800">中国高血压防治指南（2023年修订版）</h3>
              <p className="text-gray-600 mt-1">中华医学会心血管病学分会</p>
              <div className="flex items-center space-x-4 mt-3">
                <span className="bg-green-100 text-green-700 px-2 py-1 text-xs rounded-full">A级证据</span>
                <span className="text-sm text-gray-600">发布日期: 2023-11-15</span>
                <span className="text-sm text-gray-600">阅读量: 15,230</span>
              </div>
            </div>
            <div className="flex space-x-2">
              <button className="bg-white text-primary px-4 py-2 rounded-lg border border-primary hover:bg-primary-50 transition-colors">
                查看详情
              </button>
              <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center">
                <Download size={16} className="mr-2" />
                下载
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 指南列表 */}
      <div className="space-y-4">
        {filteredGuidelines.map((guideline) => {
          const evidenceConfig = getEvidenceConfig(guideline.evidenceLevel);
          
          return (
            <div key={guideline.id} className="glass-card rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">{guideline.title}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${evidenceConfig.bg} ${evidenceConfig.color} border ${evidenceConfig.border}`}>
                      {evidenceConfig.label}
                    </span>
                  </div>

                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                    <span>{guideline.organization}</span>
                    <span>•</span>
                    <span>{guideline.version}</span>
                    <span>•</span>
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-1" />
                      {guideline.publishDate}
                    </div>
                    <span>•</span>
                    <div className="flex items-center">
                      <Tag size={14} className="mr-1" />
                      {guideline.category}
                    </div>
                  </div>

                  <p className="text-gray-700 mb-4">{guideline.summary}</p>

                  {/* 要点列表 */}
                  <div className="mb-4">
                    <h4 className="font-medium text-gray-800 mb-2">核心要点:</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {guideline.keyPoints.map((point, index) => (
                        <div key={index} className="flex items-center text-sm text-gray-700">
                          <div className="w-1.5 h-1.5 bg-primary rounded-full mr-2"></div>
                          {point}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="ml-6 flex flex-col space-y-2">
                  <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center">
                    <Download size={16} className="mr-2" />
                    下载
                  </button>
                  <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                    <Star size={16} className="mr-2" />
                    收藏
                  </button>
                  <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors flex items-center">
                    <ExternalLink size={16} className="mr-2" />
                    分享
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 最近浏览 */}
      <div className="mt-8 glass-card rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">📖 最近浏览</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText size={16} className="text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">急性ST段抬高型心肌梗死诊疗指南</p>
                <p className="text-sm text-gray-600">2小时前阅读</p>
              </div>
            </div>
            <button className="text-primary hover:text-primary-600 transition-colors">
              继续阅读
            </button>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <FileText size={16} className="text-gray-500" />
              <div>
                <p className="font-medium text-gray-800">2型糖尿病防治指南</p>
                <p className="text-sm text-gray-600">昨天阅读</p>
              </div>
            </div>
            <button className="text-primary hover:text-primary-600 transition-colors">
              继续阅读
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuidelinesPage;
