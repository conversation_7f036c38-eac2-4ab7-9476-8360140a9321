import React, { useState } from 'react';
import { 
  Microscope, 
  Search, 
  Filter,
  ExternalLink,
  Calendar,
  Users,
  TrendingUp,
  BookOpen,
  Award,
  Globe,
  Star,
  Clock
} from 'lucide-react';
import { mockLiterature } from '../data/mockData';

const ResearchPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedJournal, setSelectedJournal] = useState<string>('all');

  const journals = [
    { value: 'all', label: '全部期刊' },
    { value: 'Circulation', label: 'Circulation' },
    { value: 'Nature Medicine', label: 'Nature Medicine' },
    { value: 'JACC', label: 'JACC' },
    { value: 'NEJM', label: 'NEJM' }
  ];

  const filteredLiterature = mockLiterature.filter(lit => {
    const matchesSearch = lit.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lit.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesJournal = selectedJournal === 'all' || lit.journal === selectedJournal;
    return matchesSearch && matchesJournal;
  });

  const conferences = [
    {
      id: '1',
      name: 'ESC Congress 2025',
      date: '2025-08-28',
      location: '伦敦',
      type: 'international',
      topics: ['心血管', '介入治疗', '心衰'],
      isVirtual: false
    },
    {
      id: '2',
      name: 'CIT 2025',
      date: '2025-07-15',
      location: '北京',
      type: 'national',
      topics: ['介入心脏病学', '冠心病'],
      isVirtual: false
    },
    {
      id: '3',
      name: 'ADA Scientific Sessions',
      date: '2025-06-25',
      location: '在线',
      type: 'international',
      topics: ['糖尿病', '内分泌'],
      isVirtual: true
    }
  ];

  const trends = [
    { topic: 'SGLT2抑制剂', growth: '+45%', papers: 127 },
    { topic: '人工智能诊断', growth: '+38%', papers: 89 },
    { topic: '远程监测', growth: '+32%', papers: 156 },
    { topic: '基因治疗', growth: '+28%', papers: 72 }
  ];

  return (
    <div className="h-full p-6 overflow-y-auto">
      {/* 顶部标题 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 flex items-center mb-4">
          <Microscope size={28} className="mr-3 text-primary" />
          学术前沿
        </h1>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">今日推送</p>
                <p className="text-2xl font-bold text-primary">12</p>
              </div>
              <BookOpen size={24} className="text-primary" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">关注会议</p>
                <p className="text-2xl font-bold text-blue-600">8</p>
              </div>
              <Calendar size={24} className="text-blue-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">收藏文献</p>
                <p className="text-2xl font-bold text-yellow-600">45</p>
              </div>
              <Star size={24} className="text-yellow-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">阅读时长</p>
                <p className="text-2xl font-bold text-green-600">2.5h</p>
              </div>
              <Clock size={24} className="text-green-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左栏：文献推荐 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 搜索和筛选 */}
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="搜索文献标题或作者"
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
                />
              </div>
              <select
                value={selectedJournal}
                onChange={(e) => setSelectedJournal(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
              >
                {journals.map((journal) => (
                  <option key={journal.value} value={journal.value}>
                    {journal.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* 今日推荐文献 */}
          <div>
            <h2 className="text-lg font-semibold text-gray-800 mb-4">📚 今日推荐文献</h2>
            <div className="space-y-4">
              {filteredLiterature.map((paper) => (
                <div key={paper.id} className="glass-card rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-800 mb-2 leading-tight">
                        {paper.title}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                        <span>{paper.authors.join(', ')}</span>
                        <span>•</span>
                        <span className="font-medium text-primary">{paper.journal}</span>
                        <span>•</span>
                        <span>{paper.publishDate}</span>
                      </div>

                      <p className="text-gray-700 mb-4 text-sm leading-relaxed">
                        {paper.abstract}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center text-sm">
                            <Award size={14} className="text-yellow-500 mr-1" />
                            <span>影响因子: {paper.impact}</span>
                          </div>
                          <div className="flex items-center text-sm">
                            <Users size={14} className="text-gray-500 mr-1" />
                            <span>引用: {paper.citationCount}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <button className="text-primary hover:text-primary-600 transition-colors">
                            <Star size={16} />
                          </button>
                          <button className="bg-primary text-white px-3 py-1 rounded text-sm hover:bg-primary-600 transition-colors flex items-center">
                            <ExternalLink size={14} className="mr-1" />
                            阅读
                          </button>
                        </div>
                      </div>

                      {/* 关键词标签 */}
                      <div className="flex items-center space-x-2 mt-3">
                        {paper.keywords.slice(0, 3).map((keyword, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {keyword}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右栏：会议和趋势 */}
        <div className="space-y-6">
          {/* 研究趋势 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <TrendingUp size={20} className="mr-2 text-green-500" />
              研究热点
            </h3>
            <div className="space-y-3">
              {trends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-800 text-sm">{trend.topic}</p>
                    <p className="text-xs text-gray-600">{trend.papers} 篇论文</p>
                  </div>
                  <div className="text-right">
                    <span className="text-green-600 font-medium text-sm">{trend.growth}</span>
                    <p className="text-xs text-gray-500">增长率</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 学术会议 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
              <Calendar size={20} className="mr-2 text-blue-500" />
              学术会议
            </h3>
            <div className="space-y-4">
              {conferences.map((conf) => (
                <div key={conf.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-2">
                    <h4 className="font-medium text-gray-800">{conf.name}</h4>
                    {conf.isVirtual && (
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 text-xs rounded">
                        在线
                      </span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-600 space-y-1">
                    <div className="flex items-center">
                      <Calendar size={14} className="mr-2" />
                      {conf.date}
                    </div>
                    <div className="flex items-center">
                      <Globe size={14} className="mr-2" />
                      {conf.location}
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-1 mt-3">
                    {conf.topics.map((topic, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 text-xs bg-primary-50 text-primary rounded"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                  
                  <button className="w-full mt-3 bg-gray-100 text-gray-700 py-2 rounded text-sm hover:bg-gray-200 transition-colors">
                    关注会议
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* AI推荐 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">🤖 AI智能推荐</h3>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 font-medium">基于您的阅读偏好</p>
                <p className="text-blue-700 mt-1">推荐关注"心衰治疗新进展"专题</p>
              </div>
              
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium">同科室医生关注</p>
                <p className="text-green-700 mt-1">《心肌梗死急性期管理》被高度关注</p>
              </div>
              
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 font-medium">即将截稿</p>
                <p className="text-yellow-700 mt-1">《中华心血管病杂志》投稿截止6月30日</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResearchPage;
