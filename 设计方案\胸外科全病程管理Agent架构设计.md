# 胸外科全病程管理Agent架构设计

## 项目概述

本项目是一个为胸外科医生提供的全病程管理系统，涵盖胸外科（肺结节、肺癌）患者从门诊到术后6个月随访的完整医疗流程。系统通过多个专业Agent协作，为医生提供智能化的患者管理、任务调度、临床决策支持等服务。

## 业务流程分析

### 胸外科全病程管理流程
根据业务文档分析，整个流程包含8个关键阶段：

1. **门诊就诊** - 预约挂号、智能导诊、健康档案建立、预住院登记
2. **入院当天** - 健康评估、术前检查、入科宣教、知识科普、器官评估
3. **手术日** - 术前约谈、健康评估、麻醉评估、术前准备、术后注意事项
4. **出院当天** - 出院指导、治疗方案、日常注意事项、呼吸训练、复查预约
5. **入院化疗** - 化疗住院、入科宣教、健康状况评估
6. **出院半个月** - 复诊提醒、预约挂号、电话随访、病理解读、康复指导
7. **出院3个月** - 重复半个月内容、监测训练情况、健康季报、阶段评估
8. **出院6个月** - 重复半个月内容、持续监测、定期随访维护

## Agent架构设计

### 1. 主控Agent（Master Coordination Agent）

**职责**：统筹协调所有子Agent，管理整个胸外科全病程流程

**核心功能**：
- 患者病程状态管理（门诊→入院→手术→出院→随访各阶段）
- 时间节点自动触发（半个月、3个月、6个月随访提醒）
- 多Agent任务分发和结果汇总
- 异常情况升级处理
- 医生工作流程优化建议

**技术特点**：
- 基于状态机的流程控制
- 事件驱动的任务调度
- 智能化的异常处理机制

### 2. 患者管理Agent（Patient Management Agent）

**职责**：支持医生进行患者信息管理和档案维护

**核心功能**：
- 患者基础信息自动整理和更新
- 健康档案智能分析（肺部CT等影像资料解读提示）
- 风险等级自动评估和更新
- 生理指征异常预警
- 患者搜索和筛选优化

**数据管理**：
- 基本信息（身高、体重、BMI自动计算）
- 文化社会信息（文化程度、婚姻状况、职业、医保类型）
- 生活方式（吸烟史、饮酒史、运动习惯）
- 既往病史（高血压、糖尿病、心脏病等）
- 家庭支持情况（独居状况、照护人、手机使用能力）

### 3. 任务管理Agent（Task Management Agent）

**职责**：协助医生管理日常医疗任务和待办事项

**核心功能**：
- 任务自动生成（基于病程阶段和患者状态）
- 任务优先级智能排序
- 紧急任务实时提醒
- 任务完成度跟踪
- 逾期任务预警和处理建议

**任务类型**：
- 用药调整（medication）
- 检查预约（examination）
- 随访安排（followup）
- 生活指导（lifestyle）

### 4. 临床决策支持Agent（Clinical Decision Support Agent）

**职责**：为医生提供循证医学决策支持

**核心功能**：
- 术前评估建议（基于患者基础信息和检查结果）
- 手术方案推荐
- 术后治疗方案制定
- 用药调整建议
- 并发症风险预测和预防建议

**决策依据**：
- 循证医学指南
- 患者个体化特征
- 历史治疗效果数据
- 风险评估模型

### 5. 宣教内容管理Agent（Patient Education Agent）

**职责**：协助医生进行患者宣教工作

**核心功能**：
- 个性化宣教内容生成（基于患者文化程度、理解能力）
- 肺结节/肺癌知识库维护
- 预康复训练方案制定（呼吸训练、抗阻训练等）
- 术前术后注意事项标准化模板
- 宣教效果评估和反馈

**预康复训练内容**：
- 第1天：认识预康复、饮食营养指导
- 第2天：呼吸训练（腹式呼吸、呼吸训练器、缩唇呼吸）
- 第4-7天：抗阻训练（坐姿抬腿、弹力带扩胸、哑铃推举）

### 6. 随访管理Agent（Follow-up Management Agent）

**职责**：支持医生进行患者长期随访管理

**核心功能**：
- 随访计划自动制定（半个月、3个月、6个月）
- 电话随访要点提示和记录模板
- 病理报告解读辅助
- 康复进度评估
- 复诊预约智能安排
- 异常情况及时预警

**随访内容**：
- 复诊提醒和预约挂号
- 电话随访和病理报告解读
- 呼吸训练指导和康复评估
- 健康咨询和体征监测

### 7. 日程安排Agent（Schedule Management Agent）

**职责**：优化医生的日程安排和工作效率

**核心功能**：
- 门诊排班优化
- 手术日程智能安排
- 患者预约时间合理分配
- 工作负荷平衡建议
- 紧急情况日程调整

### 8. 数据分析Agent（Analytics Agent）

**职责**：为医生提供数据洞察和绩效分析

**核心功能**：
- 患者满意度分析
- 治疗效果统计
- 工作效率分析
- 科室绩效指标监控
- 改进建议生成

## 前端页面与Agent对应关系

### Dashboard页面
- **主控Agent**：提供整体概览和协调
- **患者管理Agent**：紧急患者提醒
- **任务管理Agent**：今日任务概览
- **日程安排Agent**：今日排班信息

### Patients页面
- **患者管理Agent**：患者列表管理、详细信息展示
- **临床决策支持Agent**：健康趋势分析、风险评估
- **随访管理Agent**：患者状态跟踪

### Tasks页面
- **任务管理Agent**：任务列表管理、优先级排序
- **主控Agent**：任务分发和协调
- **临床决策支持Agent**：任务处理建议

### Calendar页面
- **日程安排Agent**：排班管理、预约安排
- **随访管理Agent**：随访计划展示

### Education页面
- **宣教内容管理Agent**：宣教材料管理
- **患者管理Agent**：个性化内容推荐

## Agent协作流程示例

### 门诊就诊阶段
1. **主控Agent**检测到新患者门诊
2. **患者管理Agent**收集基础信息，建立健康档案
3. **临床决策支持Agent**进行初步风险评估
4. **任务管理Agent**生成术前检查任务
5. **宣教内容管理Agent**准备个性化宣教材料

### 手术日阶段
1. **主控Agent**触发手术日流程
2. **任务管理Agent**生成术前准备清单
3. **临床决策支持Agent**提供手术方案建议
4. **日程安排Agent**协调手术室安排

### 随访阶段
1. **主控Agent**根据时间节点触发随访
2. **随访管理Agent**生成随访计划和要点
3. **患者管理Agent**更新患者状态
4. **数据分析Agent**分析康复效果

## 技术架构特点

### 1. 时间驱动的状态机
- 基于患者病程阶段的状态转换
- 自动化的时间节点触发机制
- 灵活的个性化调整能力

### 2. 多模态交互
- 文字指导 + 视频演示
- 语音提醒 + 图文推送
- 表单填写 + 数据收集

### 3. 个性化适配
- 基于患者基础信息的个性化方案
- 根据康复进度的动态调整
- 考虑家庭支持情况的差异化服务

### 4. 数据驱动决策
- 患者健康数据持续收集
- 康复效果实时评估
- 基于数据的方案优化

## 实施建议

1. **分阶段实施**：优先实现主控Agent和患者管理Agent
2. **数据标准化**：建立统一的患者数据模型和接口规范
3. **医生反馈机制**：建立持续的用户反馈和系统优化机制
4. **安全性保障**：确保患者隐私数据的安全性和合规性

---

*文档版本：v1.0*  
*创建日期：2025-08-05*  
*最后更新：2025-08-05*
