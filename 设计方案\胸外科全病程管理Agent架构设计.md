# 胸外科全病程管理Agent架构设计

## 项目概述

本项目是一个为胸外科医生提供的全病程管理系统，涵盖胸外科（肺结节、肺癌）患者从门诊到术后6个月随访的完整医疗流程。系统通过多个专业Agent协作，为医生提供智能化的患者管理、任务调度、临床决策支持等服务。

## 业务流程分析

### 胸外科全病程管理流程
根据业务文档分析，整个流程包含8个关键阶段：

1. **门诊就诊** - 预约挂号、智能导诊、健康档案建立、预住院登记
2. **入院当天** - 健康评估、术前检查、入科宣教、知识科普、器官评估
3. **手术日** - 术前约谈、健康评估、麻醉评估、术前准备、术后注意事项
4. **出院当天** - 出院指导、治疗方案、日常注意事项、呼吸训练、复查预约
5. **入院化疗** - 化疗住院、入科宣教、健康状况评估
6. **出院半个月** - 复诊提醒、预约挂号、电话随访、病理解读、康复指导
7. **出院3个月** - 重复半个月内容、监测训练情况、健康季报、阶段评估
8. **出院6个月** - 重复半个月内容、持续监测、定期随访维护

## Agent架构设计

### 1. 主控Agent（Master Coordination Agent）

**职责**：统筹协调所有子Agent，管理整个胸外科全病程流程

**核心功能**：
- 患者病程状态管理（门诊→入院→手术→出院→随访各阶段）
- 时间节点自动触发（半个月、3个月、6个月随访提醒）
- 多Agent任务分发和结果汇总
- 异常情况升级处理
- 医生工作流程优化建议

**技术特点**：
- 基于状态机的流程控制
- 事件驱动的任务调度
- 智能化的异常处理机制

### 2. 患者管理Agent（Patient Management Agent）

**职责**：支持医生进行患者信息管理和档案维护

**核心功能**：
- 患者基础信息自动整理和更新
- 健康档案智能分析（肺部CT等影像资料解读提示）
- 风险等级自动评估和更新
- 生理指征异常预警
- 患者搜索和筛选优化

**数据管理**：
- 基本信息（身高、体重、BMI自动计算）
- 文化社会信息（文化程度、婚姻状况、职业、医保类型）
- 生活方式（吸烟史、饮酒史、运动习惯）
- 既往病史（高血压、糖尿病、心脏病等）
- 家庭支持情况（独居状况、照护人、手机使用能力）

### 3. 任务管理Agent（Task Management Agent）

**职责**：协助医生管理日常医疗任务和待办事项

**核心功能**：
- 任务自动生成（基于病程阶段和患者状态）
- 任务优先级智能排序
- 紧急任务实时提醒
- 任务完成度跟踪
- 逾期任务预警和处理建议

**任务类型**：
- 用药调整（medication）
- 检查预约（examination）
- 随访安排（followup）
- 生活指导（lifestyle）
- 康复训练（rehabilitation）- 包括呼吸训练、有氧运动、抗阻训练

### 4. 临床决策支持Agent（Clinical Decision Support Agent）

**职责**：为医生提供循证医学决策支持

**核心功能**：
- 术前评估建议（基于患者基础信息和检查结果）
- 手术方案推荐
- 术后治疗方案制定
- 用药调整建议
- 并发症风险预测和预防建议

**决策依据**：
- 循证医学指南
- 患者个体化特征
- 历史治疗效果数据
- 风险评估模型

### 5. 宣教内容管理Agent（Patient Education Agent）

**职责**：协助医生进行患者宣教工作

**核心功能**：
- 个性化宣教内容生成（基于患者文化程度、理解能力）
- 肺结节/肺癌知识库维护
- 预康复训练方案制定（呼吸训练、有氧运动、抗阻训练等）
- 术前术后注意事项标准化模板
- 宣教效果评估和反馈
- 运动安全指导和风险评估

**预康复训练内容**：
- 第1天：认识预康复、饮食营养指导
- 第2天：呼吸训练（腹式呼吸、呼吸训练器、缩唇呼吸）
- 第3天：有氧运动（快走、爬楼梯、站立踏步，"微喘不累"原则，每天20-30分钟）
- 第4-7天：抗阻训练（坐姿抬腿、弹力带扩胸、哑铃推举）

**运动安全原则**：
- 以"微喘不累"为有氧运动强度标准
- 运动前必须进行热身，运动后进行放松
- 安全第一，根据患者个体情况调整运动强度
- 提供配套视频指导（术前慢走训练、科学热身方法）

### 6. 随访管理Agent（Follow-up Management Agent）

**职责**：支持医生进行患者长期随访管理

**核心功能**：
- 随访计划自动制定（半个月、3个月、6个月）
- 电话随访要点提示和记录模板
- 病理报告解读辅助
- 康复进度评估
- 复诊预约智能安排
- 异常情况及时预警

**随访内容**：
- 复诊提醒和预约挂号
- 电话随访和病理报告解读
- 呼吸训练指导和康复评估
- 健康咨询和体征监测

### 7. 日程安排Agent（Schedule Management Agent）

**职责**：优化医生的日程安排和工作效率

**核心功能**：
- 门诊排班优化
- 手术日程智能安排
- 患者预约时间合理分配
- 工作负荷平衡建议
- 紧急情况日程调整

### 8. 数据分析Agent（Analytics Agent）

**职责**：为医生提供数据洞察和绩效分析

**核心功能**：
- 患者满意度分析
- 治疗效果统计
- 工作效率分析
- 科室绩效指标监控
- 改进建议生成

## 前端页面与Agent对应关系

### Dashboard页面
- **主控Agent**：提供整体概览和协调
- **患者管理Agent**：紧急患者提醒
- **任务管理Agent**：今日任务概览
- **日程安排Agent**：今日排班信息

### Patients页面
- **患者管理Agent**：患者列表管理、详细信息展示
- **临床决策支持Agent**：健康趋势分析、风险评估
- **随访管理Agent**：患者状态跟踪

### Tasks页面
- **任务管理Agent**：任务列表管理、优先级排序
- **主控Agent**：任务分发和协调
- **临床决策支持Agent**：任务处理建议

### Calendar页面
- **日程安排Agent**：排班管理、预约安排
- **随访管理Agent**：随访计划展示

### Education页面
- **宣教内容管理Agent**：宣教材料管理
- **患者管理Agent**：个性化内容推荐

## Agent组织架构详解

### 架构层次结构

```
主控Agent (Master Coordination Agent)
├── 患者管理Agent (Patient Management Agent)
├── 任务管理Agent (Task Management Agent)
├── 临床决策支持Agent (Clinical Decision Support Agent)
├── 宣教内容管理Agent (Patient Education Agent)
├── 随访管理Agent (Follow-up Management Agent)
├── 日程安排Agent (Schedule Management Agent)
└── 数据分析Agent (Analytics Agent)
```

### 架构特点

1. **主控Agent作为中央协调器**
   - 负责整体流程控制和状态管理
   - 协调各个专业Agent的工作
   - 处理异常情况和升级机制

2. **专业Agent各司其职**
   - 每个Agent专注于特定领域的功能
   - 具有相对独立的业务逻辑
   - 通过标准接口与主控Agent通信

3. **事件驱动的协作模式**
   - 基于患者病程状态变化触发相应流程
   - 时间节点自动触发相关任务
   - 异常事件实时响应处理

## 具体协作场景详解

### 场景1：新患者门诊就诊

**触发条件**：患者首次门诊预约

**协作流程**：

1. **主控Agent**检测到新患者门诊事件
   ```
   事件：新患者预约 → 状态：门诊就诊阶段
   ```

2. **日程安排Agent**优化门诊时间安排
   ```
   输入：患者基本信息、医生排班
   输出：最优预约时间建议
   ```

3. **患者管理Agent**建立患者档案
   ```
   收集：基本信息表单（身高、体重、既往病史等）
   处理：BMI自动计算、风险初步评估
   输出：完整患者档案
   ```

4. **临床决策支持Agent**进行初步评估
   ```
   输入：患者档案、症状描述
   分析：手术适应症、风险等级
   输出：初步诊疗建议
   ```

5. **任务管理Agent**生成后续任务
   ```
   生成任务：
   - 术前检查预约（CT、血液检查等）
   - 预康复训练计划制定
   - 入院准备清单
   ```

6. **宣教内容管理Agent**准备教育材料
   ```
   基于：患者文化程度、理解能力
   生成：个性化宣教内容
   包含：疾病知识、预康复指导
   ```

### 场景2：预康复训练管理

**触发条件**：患者进入预康复阶段

**协作流程**：

1. **主控Agent**启动预康复流程
   ```
   状态转换：门诊就诊 → 预康复训练
   时间管理：7天训练计划启动
   ```

2. **宣教内容管理Agent**推送每日训练内容
   ```
   第1天：认识预康复 + 营养指导
   第2天：呼吸训练指导
   第3天：有氧运动方案
   第4-7天：抗阻训练计划
   ```

3. **任务管理Agent**生成每日任务
   ```
   任务类型：康复训练(rehabilitation)
   具体内容：
   - 呼吸训练打卡
   - 有氧运动记录
   - 抗阻训练完成度
   ```

4. **患者管理Agent**监控训练效果
   ```
   收集：患者训练反馈、身体状况
   分析：训练适应性、风险评估
   预警：异常情况及时提醒
   ```

5. **临床决策支持Agent**调整训练方案
   ```
   基于：患者反馈、身体状况
   调整：运动强度、训练频率
   建议：个性化优化方案
   ```

### 场景3：手术日管理

**触发条件**：患者手术日到来

**协作流程**：

1. **主控Agent**触发手术日流程
   ```
   状态转换：预康复训练 → 手术日
   流程启动：术前准备检查清单
   ```

2. **日程安排Agent**协调手术安排
   ```
   确认：手术室时间、麻醉师安排
   协调：多科室配合时间
   优化：手术流程效率
   ```

3. **任务管理Agent**生成术前任务
   ```
   紧急任务：
   - 术前禁食禁水确认
   - 术前用药检查
   - 麻醉评估完成
   - 手术同意书签署
   ```

4. **临床决策支持Agent**提供手术建议
   ```
   评估：患者术前状态
   建议：手术方案选择
   预警：潜在风险提示
   ```

5. **患者管理Agent**更新患者状态
   ```
   记录：术前各项指标
   监控：生理指征变化
   预警：异常情况处理
   ```

### 场景4：术后随访管理

**触发条件**：出院后时间节点到达（半个月/3个月/6个月）

**协作流程**：

1. **主控Agent**触发随访流程
   ```
   时间检测：出院后半个月
   状态转换：住院治疗 → 随访阶段
   流程启动：随访计划执行
   ```

2. **随访管理Agent**制定随访计划
   ```
   随访内容：
   - 复诊提醒和预约
   - 电话随访要点
   - 病理报告解读
   - 康复进度评估
   ```

3. **任务管理Agent**生成随访任务
   ```
   任务分配：
   - 电话随访执行
   - 复诊预约安排
   - 康复指导更新
   - 异常情况处理
   ```

4. **数据分析Agent**分析康复效果
   ```
   数据收集：康复训练数据、生理指标
   趋势分析：恢复进度评估
   效果评价：治疗效果统计
   ```

5. **临床决策支持Agent**调整治疗方案
   ```
   基于：随访数据分析结果
   调整：康复训练强度
   建议：后续治疗方案
   ```

## Agent间通信机制

### 1. 事件总线模式
```
事件发布 → 主控Agent → 相关Agent订阅 → 执行响应
```

**特点**：
- 松耦合的Agent间通信
- 支持一对多的事件广播
- 异步处理提高系统响应性

### 2. 数据共享机制
```
患者数据中心 ← → 各专业Agent
实时同步患者状态和处理结果
```

**特点**：
- 统一的数据存储和访问接口
- 实时数据同步保证一致性
- 权限控制确保数据安全

### 3. 任务调度机制
```
主控Agent → 任务队列 → 专业Agent执行 → 结果反馈
```

**特点**：
- 基于优先级的任务调度
- 支持任务的并行和串行执行
- 完整的任务生命周期管理

## 技术架构特点

### 1. 时间驱动的状态机
- 基于患者病程阶段的状态转换
- 自动化的时间节点触发机制
- 灵活的个性化调整能力

### 2. 多模态交互
- 文字指导 + 视频演示
- 语音提醒 + 图文推送
- 表单填写 + 数据收集

### 3. 个性化适配
- 基于患者基础信息的个性化方案
- 根据康复进度的动态调整
- 考虑家庭支持情况的差异化服务

### 4. 数据驱动决策
- 患者健康数据持续收集
- 康复效果实时评估
- 基于数据的方案优化

### 5. 高可用性设计
- **统一协调**：主控Agent统筹全局
- **专业分工**：各Agent专注专业领域
- **高效协作**：基于事件驱动的实时响应
- **灵活扩展**：新增Agent不影响现有架构

## 实施建议

1. **分阶段实施**：优先实现主控Agent和患者管理Agent
2. **数据标准化**：建立统一的患者数据模型和接口规范
3. **医生反馈机制**：建立持续的用户反馈和系统优化机制
4. **安全性保障**：确保患者隐私数据的安全性和合规性

---

*文档版本：v1.0*  
*创建日期：2025-08-05*  
*最后更新：2025-08-05*
