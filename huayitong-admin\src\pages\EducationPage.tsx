import React, { useState } from 'react';
import { 
  GraduationCap, 
  Play, 
  Clock, 
  Users, 
  Star,
  BookOpen,
  Video,
  Mic,
  Search,
  Filter,
  Award,
  TrendingUp
} from 'lucide-react';
import { mockCourses } from '../data/mockData';

const EducationPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { value: 'all', label: '全部课程' },
    { value: '心血管', label: '心血管' },
    { value: '内分泌', label: '内分泌' },
    { value: '呼吸科', label: '呼吸科' },
    { value: '肾内科', label: '肾内科' }
  ];

  const filteredCourses = mockCourses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.instructor.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getLevelConfig = (level: string) => {
    switch (level) {
      case 'beginner':
        return { label: '初级', color: 'text-green-600', bg: 'bg-green-50' };
      case 'intermediate':
        return { label: '中级', color: 'text-yellow-600', bg: 'bg-yellow-50' };
      case 'advanced':
        return { label: '高级', color: 'text-red-600', bg: 'bg-red-50' };
      default:
        return { label: '未知', color: 'text-gray-600', bg: 'bg-gray-50' };
    }
  };

  return (
    <div className="h-full p-6 overflow-y-auto">
      {/* 顶部标题和统计 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center">
            <GraduationCap size={28} className="mr-3 text-primary" />
            临床课堂
          </h1>
          <div className="flex items-center space-x-2">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
              <Video size={18} className="mr-2" />
              直播中心
            </button>
            <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center">
              <BookOpen size={18} className="mr-2" />
              我的课程
            </button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">本周学习</p>
                <p className="text-2xl font-bold text-primary">12.5</p>
                <p className="text-xs text-gray-500">小时</p>
              </div>
              <Clock size={24} className="text-primary" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">完成课程</p>
                <p className="text-2xl font-bold text-green-600">28</p>
                <p className="text-xs text-gray-500">门</p>
              </div>
              <Award size={24} className="text-green-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">CME学分</p>
                <p className="text-2xl font-bold text-blue-600">156</p>
                <p className="text-xs text-gray-500">分</p>
              </div>
              <Star size={24} className="text-blue-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">学习排名</p>
                <p className="text-2xl font-bold text-orange-600">TOP 5%</p>
                <p className="text-xs text-gray-500">科室内</p>
              </div>
              <TrendingUp size={24} className="text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="glass-card rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索课程名称或讲师"
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            />
          </div>

          {/* 分类筛选 */}
          <div className="flex space-x-2">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-3 py-2 text-sm rounded-lg border transition-colors ${
                  selectedCategory === category.value
                    ? 'bg-primary text-white border-primary'
                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 直播推荐 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
          <Mic size={20} className="mr-2 text-red-500" />
          正在直播
        </h2>
        
        <div className="glass-card rounded-lg p-6 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-red-100 rounded-lg flex items-center justify-center">
                <Video size={24} className="text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-800">糖尿病并发症防治策略</h3>
                <p className="text-gray-600">王教授 · 内分泌科</p>
                <div className="flex items-center space-x-4 mt-2">
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
                    <span className="text-sm text-red-600 font-medium">正在直播</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Users size={14} className="mr-1" />
                    892人在线
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock size={14} className="mr-1" />
                    进行中 (19:00-20:00)
                  </div>
                </div>
              </div>
            </div>
            <button className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors flex items-center">
              <Play size={18} className="mr-2" />
              加入直播
            </button>
          </div>
        </div>
      </div>

      {/* 推荐课程 */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">🎯 为您推荐</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCourses.map((course) => {
            const levelConfig = getLevelConfig(course.level);
            
            return (
              <div key={course.id} className="glass-card rounded-lg overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                {/* 课程缩略图 */}
                <div className="relative h-48 bg-gradient-to-br from-primary-50 to-primary-100">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Play size={48} className="text-primary opacity-60" />
                  </div>
                  
                  {/* 直播标识 */}
                  {course.isLive && (
                    <div className="absolute top-3 left-3 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse mr-1"></div>
                      直播
                    </div>
                  )}
                  
                  {/* 课程时长 */}
                  <div className="absolute bottom-3 right-3 bg-black/70 text-white px-2 py-1 rounded text-xs">
                    {course.duration}
                  </div>
                </div>

                {/* 课程信息 */}
                <div className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${levelConfig.bg} ${levelConfig.color}`}>
                      {levelConfig.label}
                    </span>
                    <span className="text-xs text-gray-500">{course.category}</span>
                  </div>
                  
                  <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">{course.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{course.instructor}</p>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <Star size={14} className="text-yellow-500 mr-1" />
                      <span className="font-medium">{course.rating}</span>
                      <span className="text-gray-500 ml-1">({course.enrollment})</span>
                    </div>
                    
                    {course.isLive && course.startTime ? (
                      <span className="text-red-600 font-medium">{course.startTime}</span>
                    ) : (
                      <div className="flex items-center text-gray-500">
                        <Users size={14} className="mr-1" />
                        {course.enrollment}
                      </div>
                    )}
                  </div>
                </div>

                {/* 操作按钮 */}
                <div className="p-4 pt-0">
                  <button className="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center justify-center">
                    <Play size={16} className="mr-2" />
                    {course.isLive ? '加入直播' : '开始学习'}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 学习进度和成就 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 最近学习 */}
        <div className="glass-card rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">📚 最近学习</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Play size={16} className="text-primary" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">急性心肌梗死的诊断与治疗</p>
                  <p className="text-sm text-gray-600">进度: 75%</p>
                </div>
              </div>
              <button className="text-primary hover:text-primary-600 transition-colors">
                继续
              </button>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Award size={16} className="text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">高血压规范化管理</p>
                  <p className="text-sm text-green-600">已完成</p>
                </div>
              </div>
              <button className="text-green-600 hover:text-green-700 transition-colors">
                回顾
              </button>
            </div>
          </div>
        </div>

        {/* 学习成就 */}
        <div className="glass-card rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">🏆 学习成就</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <Award size={20} className="text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">心血管专家</p>
                  <p className="text-sm text-gray-600">完成15门心血管课程</p>
                </div>
              </div>
              <span className="text-yellow-600 font-medium">已获得</span>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Star size={20} className="text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800">学习达人</p>
                  <p className="text-sm text-gray-600">连续学习30天</p>
                </div>
              </div>
              <span className="text-blue-600 font-medium">已获得</span>
            </div>
            
            <div className="flex items-center justify-between opacity-60">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                  <GraduationCap size={20} className="text-gray-500" />
                </div>
                <div>
                  <p className="font-medium text-gray-500">全科医师</p>
                  <p className="text-sm text-gray-400">完成50门不同科室课程</p>
                </div>
              </div>
              <span className="text-gray-400">35/50</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EducationPage;
