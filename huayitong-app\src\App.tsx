import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import BottomNav from './components/common/BottomNav';
import HomePage from './pages/HomePage';
import KnowledgePage from './pages/KnowledgePage';
import CardPage from './pages/CardPage';
import DataPage from './pages/DataPage';
import ProfilePage from './pages/ProfilePage';

function App() {
  return (
    <Router>
      <div className="App">
        {/* 页面内容 */}
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/knowledge" element={<KnowledgePage />} />
          <Route path="/card" element={<CardPage />} />
          <Route path="/data" element={<DataPage />} />
          <Route path="/profile" element={<ProfilePage />} />
        </Routes>
        
        {/* 底部导航栏 */}
        <BottomNav />
      </div>
    </Router>
  );
}

export default App;
