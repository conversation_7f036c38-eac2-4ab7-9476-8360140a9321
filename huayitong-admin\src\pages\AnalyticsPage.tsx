import React, { useState } from 'react';
import { 
  Bar<PERSON>hart3, 
  TrendingUp,
  Users,
  Activity,
  Calendar,
  FileText,
  Download,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Table,
  Filter,
  Plus,
  Eye
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Cell, LineChart as <PERSON><PERSON>ine<PERSON><PERSON>, Line } from 'recharts';

const AnalyticsPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedDataType, setSelectedDataType] = useState('patients');

  // 模拟数据分析数据
  const patientStatsData = [
    { name: '高血压', value: 45, percentage: 35 },
    { name: '糖尿病', value: 32, percentage: 25 },
    { name: '冠心病', value: 28, percentage: 22 },
    { name: '心衰', value: 15, percentage: 12 },
    { name: '其他', value: 8, percentage: 6 }
  ];

  const monthlyTrendData = [
    { month: '1月', patients: 120, satisfaction: 4.6, efficiency: 85 },
    { month: '2月', patients: 135, satisfaction: 4.7, efficiency: 87 },
    { month: '3月', patients: 142, satisfaction: 4.8, efficiency: 89 },
    { month: '4月', patients: 158, satisfaction: 4.7, efficiency: 88 },
    { month: '5月', patients: 165, satisfaction: 4.9, efficiency: 92 },
    { month: '6月', patients: 178, satisfaction: 4.8, efficiency: 90 }
  ];

  const treatmentOutcomes = [
    { treatment: '药物治疗', success: 85, improvement: 92, stable: 89 },
    { treatment: '介入治疗', success: 94, improvement: 96, stable: 91 },
    { treatment: '手术治疗', success: 91, improvement: 88, stable: 94 },
    { treatment: '综合治疗', success: 93, improvement: 95, stable: 92 }
  ];

  const researchProjects = [
    {
      id: '1',
      title: '高血压患者远程监测效果研究',
      status: 'active',
      participants: 156,
      completion: 75,
      startDate: '2024-01-15',
      expectedEnd: '2025-07-31'
    },
    {
      id: '2',
      title: 'AI辅助心衰诊断准确性评估',
      status: 'recruiting',
      participants: 89,
      completion: 45,
      startDate: '2024-03-10',
      expectedEnd: '2025-09-30'
    },
    {
      id: '3',
      title: '个性化用药方案优化研究',
      status: 'completed',
      participants: 203,
      completion: 100,
      startDate: '2023-08-20',
      expectedEnd: '2024-12-15'
    }
  ];

  const COLORS = ['#00B4A6', '#3B82F6', '#EF4444', '#F59E0B', '#8B5CF6'];

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return { label: '进行中', color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' };
      case 'recruiting':
        return { label: '招募中', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' };
      case 'completed':
        return { label: '已完成', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
      default:
        return { label: '未知', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  return (
    <div className="h-full p-6 overflow-y-auto">
      {/* 顶部标题 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center">
            <BarChart3 size={28} className="mr-3 text-primary" />
            科研助手
          </h1>
          <div className="flex items-center space-x-2">
            <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center">
              <Plus size={18} className="mr-2" />
              新建研究
            </button>
            <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center">
              <Download size={18} className="mr-2" />
              导出报告
            </button>
          </div>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">数据记录</p>
                <p className="text-2xl font-bold text-primary">1,247</p>
                <p className="text-xs text-gray-500">条</p>
              </div>
              <BarChart3 size={24} className="text-primary" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">进行项目</p>
                <p className="text-2xl font-bold text-blue-600">3</p>
                <p className="text-xs text-gray-500">个</p>
              </div>
              <Activity size={24} className="text-blue-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">发表论文</p>
                <p className="text-2xl font-bold text-green-600">12</p>
                <p className="text-xs text-gray-500">篇</p>
              </div>
              <FileText size={24} className="text-green-600" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">研究参与者</p>
                <p className="text-2xl font-bold text-orange-600">448</p>
                <p className="text-xs text-gray-500">人</p>
              </div>
              <Users size={24} className="text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 左栏：数据分析 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 患者构成分析 */}
          <div className="glass-card rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-800">📊 患者构成分析</h2>
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-3 py-1 border border-gray-200 rounded text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none"
              >
                <option value="week">本周</option>
                <option value="month">本月</option>
                <option value="year">本年</option>
              </select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 饼图 */}
              <div>
                <h3 className="font-medium text-gray-700 mb-3">疾病分布</h3>
                <ResponsiveContainer width="100%" height={200}>
                  <RePieChart>
                    <Pie
                      data={patientStatsData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                    >
                      {patientStatsData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RePieChart>
                </ResponsiveContainer>
              </div>
              
              {/* 统计列表 */}
              <div>
                <h3 className="font-medium text-gray-700 mb-3">详细数据</h3>
                <div className="space-y-2">
                  {patientStatsData.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: COLORS[index % COLORS.length] }}
                        ></div>
                        <span className="text-sm text-gray-700">{item.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium text-gray-800">{item.value}</span>
                        <span className="text-xs text-gray-500 ml-1">({item.percentage}%)</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* 趋势分析 */}
          <div className="glass-card rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">📈 月度趋势分析</h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <ReLineChart data={monthlyTrendData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="month" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'white', 
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="patients" 
                    stroke="#00B4A6" 
                    strokeWidth={2}
                    dot={{ fill: '#00B4A6', strokeWidth: 2, r: 4 }}
                    name="患者数量"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="efficiency" 
                    stroke="#3B82F6" 
                    strokeWidth={2}
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    name="治疗效率(%)"
                  />
                </ReLineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* 治疗效果分析 */}
          <div className="glass-card rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">🎯 治疗效果分析</h2>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={treatmentOutcomes}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="treatment" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#888' }}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'white', 
                      border: '1px solid #e5e7eb',
                      borderRadius: '8px',
                      fontSize: '12px'
                    }}
                  />
                  <Bar dataKey="success" fill="#00B4A6" name="治愈率" />
                  <Bar dataKey="improvement" fill="#3B82F6" name="改善率" />
                  <Bar dataKey="stable" fill="#F59E0B" name="稳定率" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>

        {/* 右栏：研究项目 */}
        <div className="space-y-6">
          {/* 研究项目管理 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">🔬 研究项目</h3>
            <div className="space-y-4">
              {researchProjects.map((project) => {
                const statusConfig = getStatusConfig(project.status);
                
                return (
                  <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-800 text-sm leading-tight">
                        {project.title}
                      </h4>
                      <span className={`px-2 py-1 text-xs rounded-full ${statusConfig.bg} ${statusConfig.color} border ${statusConfig.border}`}>
                        {statusConfig.label}
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-xs text-gray-600">
                      <div className="flex justify-between">
                        <span>参与者:</span>
                        <span className="font-medium">{project.participants} 人</span>
                      </div>
                      <div className="flex justify-between">
                        <span>完成度:</span>
                        <span className="font-medium">{project.completion}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>开始时间:</span>
                        <span>{project.startDate}</span>
                      </div>
                    </div>
                    
                    {/* 进度条 */}
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${project.completion}%` }}
                        ></div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2 mt-3">
                      <button className="flex-1 bg-gray-100 text-gray-700 py-1 rounded text-xs hover:bg-gray-200 transition-colors">
                        查看详情
                      </button>
                      <button className="flex-1 bg-primary text-white py-1 rounded text-xs hover:bg-primary-600 transition-colors">
                        数据分析
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* 智能分析建议 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">🤖 AI分析建议</h3>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                <p className="text-blue-800 font-medium text-sm">数据质量评估</p>
                <p className="text-blue-700 mt-1 text-xs">
                  数据完整性为93%，建议补充缺失的血压监测记录
                </p>
              </div>
              
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800 font-medium text-sm">统计显著性</p>
                <p className="text-green-700 mt-1 text-xs">
                  发现治疗方案A与B之间存在显著差异(p&lt;0.05)
                </p>
              </div>
              
              <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-yellow-800 font-medium text-sm">样本量建议</p>
                <p className="text-yellow-700 mt-1 text-xs">
                  建议增加样本量至200例以提高统计效力
                </p>
              </div>
            </div>
          </div>

          {/* 论文写作助手 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">✍️ 论文写作助手</h3>
            <div className="space-y-3">
              <button className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                📊 生成统计图表
              </button>
              <button className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                📝 智能摘要生成
              </button>
              <button className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors text-sm">
                📖 文献引用检索
              </button>
              <button className="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-600 transition-colors text-sm">
                🚀 开始写作
              </button>
            </div>
          </div>

          {/* 数据导出 */}
          <div className="glass-card rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">📁 数据导出</h3>
            <div className="space-y-2">
              <button className="w-full flex items-center justify-between bg-gray-50 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors text-sm">
                <span>患者数据 (Excel)</span>
                <Download size={14} />
              </button>
              <button className="w-full flex items-center justify-between bg-gray-50 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors text-sm">
                <span>统计报告 (PDF)</span>
                <Download size={14} />
              </button>
              <button className="w-full flex items-center justify-between bg-gray-50 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-100 transition-colors text-sm">
                <span>原始数据 (CSV)</span>
                <Download size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsPage;
