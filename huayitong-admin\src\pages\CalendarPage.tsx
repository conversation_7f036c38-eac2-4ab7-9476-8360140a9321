import React, { useState } from 'react';
import { 
  Calendar,
  ChevronLeft,
  ChevronRight,
  Clock,
  User,
  MapPin,
  Phone,
  Plus,
  Search,
  Filter,
  Settings,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { mockAppointments, mockPatients, Appointment } from '../data/mockData';

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date(2025, 5, 20)); // 2025年6月20日
  const [viewMode, setViewMode] = useState<'week' | 'day'>('day');
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);

  // 生成当前周的日期
  const getCurrentWeekDates = () => {
    const startOfWeek = new Date(currentDate);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // 从周一开始
    startOfWeek.setDate(diff);
    
    const weekDates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      weekDates.push(date);
    }
    return weekDates;
  };

  // 生成时间段
  const timeSlots = [];
  for (let hour = 8; hour < 18; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);
    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);
  }

  // 获取指定日期的预约
  const getAppointmentsForDate = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0];
    return mockAppointments.filter(apt => 
      apt.time.startsWith(dateStr.split('-').reverse().join('-'))
    );
  };

  // 获取当天预约
  const todayAppointments = getAppointmentsForDate(currentDate);

  // 导航到前一天/下一天
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    if (viewMode === 'day') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 1 : -1));
    } else {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7));
    }
    setCurrentDate(newDate);
  };

  // 获取预约的状态配置
  const getAppointmentConfig = (appointment: Appointment) => {
    switch (appointment.status) {
      case 'completed':
        return { bg: 'bg-green-100', border: 'border-green-300', text: 'text-green-800' };
      case 'cancelled':
        return { bg: 'bg-red-100', border: 'border-red-300', text: 'text-red-800' };
      default:
        return { bg: 'bg-blue-100', border: 'border-blue-300', text: 'text-blue-800' };
    }
  };

  // 获取预约类型配置
  const getTypeConfig = (type: string) => {
    switch (type) {
      case 'emergency':
        return { label: '急诊', color: 'text-red-600', bg: 'bg-red-50' };
      case 'followup':
        return { label: '复诊', color: 'text-blue-600', bg: 'bg-blue-50' };
      default:
        return { label: '初诊', color: 'text-green-600', bg: 'bg-green-50' };
    }
  };

  return (
    <div className="h-full flex">
      {/* 左侧日历视图 */}
      <div className="flex-1 bg-white">
        {/* 顶部工具栏 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-bold text-gray-800 flex items-center">
                <Calendar size={24} className="mr-2 text-primary" />
                出诊日历
              </h1>
              
              {/* 日期导航 */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => navigateDate('prev')}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronLeft size={18} />
                </button>
                
                <div className="text-center min-w-40">
                  <h2 className="text-lg font-semibold text-gray-800">
                    {currentDate.toLocaleDateString('zh-CN', { 
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long'
                    })}
                  </h2>
                </div>
                
                <button
                  onClick={() => navigateDate('next')}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ChevronRight size={18} />
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* 视图切换 */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('day')}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    viewMode === 'day' ? 'bg-white text-gray-800 shadow-sm' : 'text-gray-600'
                  }`}
                >
                  日视图
                </button>
                <button
                  onClick={() => setViewMode('week')}
                  className={`px-3 py-1 text-sm rounded transition-colors ${
                    viewMode === 'week' ? 'bg-white text-gray-800 shadow-sm' : 'text-gray-600'
                  }`}
                >
                  周视图
                </button>
              </div>
              
              <button className="p-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors">
                <Plus size={18} />
              </button>
            </div>
          </div>
        </div>

        {/* 日历内容区域 */}
        <div className="flex-1 p-4">
          {viewMode === 'day' ? (
            /* 日视图 */
            <div className="grid grid-cols-12 gap-4 h-full">
              {/* 时间轴 */}
              <div className="col-span-1">
                <div className="space-y-6">
                  {timeSlots.map((time, index) => (
                    <div key={index} className="text-xs text-gray-500 h-12 flex items-start">
                      {time}
                    </div>
                  ))}
                </div>
              </div>

              {/* 预约时间段 */}
              <div className="col-span-11 relative">
                <div className="space-y-6">
                  {timeSlots.map((time, index) => {
                    const appointment = todayAppointments.find(apt => 
                      apt.time.includes(time)
                    );
                    
                    return (
                      <div key={index} className="h-12 border-b border-gray-100 relative">
                        {appointment && (
                          <div
                            onClick={() => setSelectedAppointment(appointment)}
                            className={`absolute inset-0 p-2 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                              getAppointmentConfig(appointment).bg
                            } ${getAppointmentConfig(appointment).border} border`}
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <p className={`font-medium text-sm ${getAppointmentConfig(appointment).text}`}>
                                  {appointment.patientName}
                                </p>
                                <p className="text-xs text-gray-600">
                                  {getTypeConfig(appointment.type).label} · {appointment.duration}分钟
                                </p>
                              </div>
                              <div className="text-right">
                                {appointment.status === 'completed' ? (
                                  <CheckCircle size={16} className="text-green-600" />
                                ) : appointment.type === 'emergency' ? (
                                  <AlertCircle size={16} className="text-red-600" />
                                ) : (
                                  <Clock size={16} className="text-blue-600" />
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ) : (
            /* 周视图 */
            <div className="grid grid-cols-8 gap-2 h-full">
              {/* 时间轴 */}
              <div className="col-span-1">
                <div className="h-12 border-b border-gray-200"></div>
                <div className="space-y-4 mt-2">
                  {[9, 10, 11, 14, 15, 16, 17].map((hour) => (
                    <div key={hour} className="text-xs text-gray-500 h-16 flex items-start">
                      {hour}:00
                    </div>
                  ))}
                </div>
              </div>

              {/* 周日期 */}
              {getCurrentWeekDates().map((date, index) => (
                <div key={index} className="col-span-1">
                  <div className={`h-12 border-b border-gray-200 p-2 text-center ${
                    date.toDateString() === currentDate.toDateString() ? 'bg-primary text-white' : ''
                  }`}>
                    <p className="text-xs">{date.toLocaleDateString('zh-CN', { weekday: 'short' })}</p>
                    <p className="font-semibold">{date.getDate()}</p>
                  </div>
                  
                  {/* 该日期的预约 */}
                  <div className="mt-2 space-y-1">
                    {getAppointmentsForDate(date).slice(0, 3).map((apt, aptIndex) => (
                      <div
                        key={aptIndex}
                        onClick={() => setSelectedAppointment(apt)}
                        className={`p-1 rounded text-xs cursor-pointer ${getAppointmentConfig(apt).bg} ${getAppointmentConfig(apt).text}`}
                      >
                        {apt.patientName}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 右侧详情面板 */}
      <div className="w-80 border-l border-gray-200 bg-gray-50">
        {selectedAppointment ? (
          <div className="p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">预约详情</h2>
            
            {/* 患者信息 */}
            <div className="glass-card rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  <User size={20} className="text-gray-500" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">{selectedAppointment.patientName}</h3>
                  <p className="text-sm text-gray-600">
                    {getTypeConfig(selectedAppointment.type).label}
                  </p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center">
                  <Clock size={14} className="text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">{selectedAppointment.time}</span>
                </div>
                <div className="flex items-center">
                  <MapPin size={14} className="text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">心内科门诊</span>
                </div>
                <div className="flex items-center">
                  <User size={14} className="text-gray-400 mr-2" />
                  <span className="text-sm text-gray-700">预约时长: {selectedAppointment.duration}分钟</span>
                </div>
              </div>
            </div>

            {/* 预约状态 */}
            <div className="glass-card rounded-lg p-4 mb-4">
              <h4 className="font-medium text-gray-800 mb-2">预约状态</h4>
              <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${
                getAppointmentConfig(selectedAppointment).bg
              } ${getAppointmentConfig(selectedAppointment).text}`}>
                {selectedAppointment.status === 'completed' ? (
                  <>
                    <CheckCircle size={14} className="mr-1" />
                    已完成
                  </>
                ) : selectedAppointment.status === 'cancelled' ? (
                  <>
                    <AlertCircle size={14} className="mr-1" />
                    已取消
                  </>
                ) : (
                  <>
                    <Clock size={14} className="mr-1" />
                    待就诊
                  </>
                )}
              </div>
            </div>

            {/* 备注信息 */}
            {selectedAppointment.notes && (
              <div className="glass-card rounded-lg p-4 mb-4">
                <h4 className="font-medium text-gray-800 mb-2">备注</h4>
                <p className="text-sm text-gray-700">{selectedAppointment.notes}</p>
              </div>
            )}

            {/* 患者健康信息 */}
            {(() => {
              const patient = mockPatients.find(p => p.name === selectedAppointment.patientName);
              if (!patient) return null;
              
              return (
                <div className="glass-card rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-gray-800 mb-3">患者信息</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">年龄:</span>
                      <span className="text-sm font-medium text-gray-800">{patient.age}岁</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">诊断:</span>
                      <span className="text-sm font-medium text-gray-800">{patient.diagnosis}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">风险等级:</span>
                      <span className={`text-sm font-medium ${
                        patient.riskLevel === 'high' ? 'text-red-600' :
                        patient.riskLevel === 'medium' ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {patient.riskLevel === 'high' ? '高风险' :
                         patient.riskLevel === 'medium' ? '中风险' : '低风险'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">血压:</span>
                      <span className="text-sm font-medium text-gray-800">{patient.vitals.bloodPressure}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">心率:</span>
                      <span className="text-sm font-medium text-gray-800">{patient.vitals.heartRate} bpm</span>
                    </div>
                  </div>
                </div>
              );
            })()}

            {/* 操作按钮 */}
            <div className="space-y-2">
              <button className="w-full bg-primary text-white py-2 rounded-lg hover:bg-primary-600 transition-colors">
                开始就诊
              </button>
              <button className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                查看病历
              </button>
              <button className="w-full bg-gray-100 text-gray-700 py-2 rounded-lg hover:bg-gray-200 transition-colors">
                重新安排
              </button>
            </div>
          </div>
        ) : (
          /* 今日概览 */
          <div className="p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">今日概览</h2>
            
            {/* 统计信息 */}
            <div className="grid grid-cols-2 gap-3 mb-6">
              <div className="glass-card rounded-lg p-3 text-center">
                <p className="text-2xl font-bold text-primary">{todayAppointments.length}</p>
                <p className="text-xs text-gray-600">总预约</p>
              </div>
              <div className="glass-card rounded-lg p-3 text-center">
                <p className="text-2xl font-bold text-green-600">
                  {todayAppointments.filter(apt => apt.status === 'completed').length}
                </p>
                <p className="text-xs text-gray-600">已完成</p>
              </div>
            </div>

            {/* 今日预约列表 */}
            <div className="space-y-3">
              <h3 className="font-medium text-gray-800">今日预约</h3>
              {todayAppointments.map((appointment) => (
                <div
                  key={appointment.id}
                  onClick={() => setSelectedAppointment(appointment)}
                  className="glass-card rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-800">{appointment.patientName}</p>
                      <p className="text-sm text-gray-600">{appointment.time.split(' ')[1]}</p>
                    </div>
                    <div className="text-right">
                      <span className={`text-xs px-2 py-1 rounded-full ${getTypeConfig(appointment.type).bg} ${getTypeConfig(appointment.type).color}`}>
                        {getTypeConfig(appointment.type).label}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* AI智能建议 */}
            <div className="mt-6 glass-card rounded-lg p-4">
              <h3 className="font-medium text-gray-800 mb-2">🤖 AI排班建议</h3>
              <div className="text-sm text-gray-700 space-y-2">
                <p>• 建议在10:00-11:00安排高风险患者</p>
                <p>• 下午时段适合安排复诊患者</p>
                <p>• 预留30分钟缓冲时间处理突发情况</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CalendarPage;
