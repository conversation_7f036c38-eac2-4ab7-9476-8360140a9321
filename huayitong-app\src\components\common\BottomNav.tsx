import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  MessageCircle, 
  BookOpen, 
  CreditCard, 
  BarChart3, 
  User 
} from 'lucide-react';

const BottomNav: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    {
      path: '/',
      icon: MessageCircle,
      label: '首页',
      activeColor: 'text-primary'
    },
    {
      path: '/knowledge',
      icon: BookOpen,
      label: '广场',
      activeColor: 'text-primary'
    },
    {
      path: '/card',
      icon: CreditCard,
      label: '就诊',
      activeColor: 'text-primary'
    },
    {
      path: '/data',
      icon: BarChart3,
      label: '数据',
      activeColor: 'text-primary'
    },
    {
      path: '/profile',
      icon: User,
      label: '我的',
      activeColor: 'text-primary'
    }
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 max-w-md mx-auto z-50">
      <div className="flex items-center justify-around py-2">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path;
          
          return (
            <button
              key={item.path}
              onClick={() => handleNavigate(item.path)}
              className={`flex flex-col items-center py-2 px-3 transition-colors ${
                isActive ? item.activeColor : 'text-gray-400'
              }`}
            >
              <Icon size={20} />
              <span className="text-xs mt-1 font-medium">{item.label}</span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default BottomNav;
