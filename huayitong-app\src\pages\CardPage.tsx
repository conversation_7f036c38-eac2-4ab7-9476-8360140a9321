import React, { useState } from 'react';
import { 
  QrCode, 
  CreditCard, 
  Calendar,
  MapPin,
  Clock,
  Phone,
  User,
  Hospital,
  ChevronRight,
  Wallet
} from 'lucide-react';

const CardPage: React.FC = () => {
  const [showQR, setShowQR] = useState(true);

  const patientInfo = {
    name: '张三',
    id: '135****0271',
    cardNumber: '2024060123456',
    department: '心内科',
    doctor: '李教授',
    nextAppointment: '2025-06-21 09:30',
    hospital: '四川大学华西医院',
    balance: '268.50'
  };

  const recentAppointments = [
    {
      id: '1',
      date: '2025-06-21',
      time: '09:30',
      department: '心内科',
      doctor: '李教授',
      status: 'upcoming',
      location: '门诊楼3楼'
    },
    {
      id: '2',
      date: '2025-06-15',
      time: '14:00',
      department: '心内科',
      doctor: '李教授',
      status: 'completed',
      location: '门诊楼3楼'
    }
  ];

  const quickServices = [
    { icon: Calendar, label: '预约挂号', color: 'bg-blue-50 text-blue-600' },
    { icon: Wallet, label: '缴费充值', color: 'bg-green-50 text-green-600' },
    { icon: MapPin, label: '科室导航', color: 'bg-purple-50 text-purple-600' },
    { icon: Phone, label: '联系客服', color: 'bg-orange-50 text-orange-600' }
  ];

  return (
    <div className="mobile-container gradient-bg">
      {/* 顶部导航 */}
      <header className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-white/20">
        <h1 className="text-xl font-bold text-gray-800">智能就诊卡</h1>
        <button className="text-primary text-sm font-medium">
          管理卡片
        </button>
      </header>

      {/* 内容区域 */}
      <div className="px-4 py-6 pb-20 space-y-6">
        
        {/* 就诊卡主卡片 */}
        <div className="relative">
          <div className="bg-gradient-to-br from-primary to-primary-600 rounded-3xl p-6 text-white shadow-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Hospital size={24} className="text-white" />
                </div>
                <div>
                  <h2 className="font-bold text-lg">{patientInfo.hospital}</h2>
                  <p className="text-primary-100 text-sm">华医通智能就诊卡</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-primary-100 text-sm">余额</p>
                <p className="font-bold text-lg">¥{patientInfo.balance}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-primary-100">姓名</span>
                <span className="font-medium">{patientInfo.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-primary-100">就诊号</span>
                <span className="font-medium">{patientInfo.cardNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-primary-100">科室</span>
                <span className="font-medium">{patientInfo.department}</span>
              </div>
            </div>

            {/* 芯片样式装饰 */}
            <div className="absolute top-4 right-4 w-8 h-6 bg-white/30 rounded opacity-50"></div>
            <div className="absolute bottom-4 right-4 w-12 h-8 bg-white/20 rounded opacity-30"></div>
          </div>
        </div>

        {/* 二维码展示 */}
        <div className="glass-card rounded-2xl p-6 text-center">
          <h3 className="font-semibold text-gray-800 mb-4">就诊二维码</h3>
          {showQR ? (
            <div className="mx-auto w-48 h-48 bg-white border-2 border-gray-200 rounded-2xl flex items-center justify-center mb-4">
              {/* 二维码占位符 */}
              <div className="w-40 h-40 bg-gray-100 rounded-lg flex flex-col items-center justify-center">
                <QrCode size={80} className="text-gray-400 mb-2" />
                <p className="text-xs text-gray-500">扫码挂号、缴费、取药</p>
              </div>
            </div>
          ) : (
            <div className="mx-auto w-48 h-48 bg-gray-100 rounded-2xl flex items-center justify-center mb-4">
              <p className="text-gray-500">二维码已隐藏</p>
            </div>
          )}
          <button 
            onClick={() => setShowQR(!showQR)}
            className="text-primary font-medium text-sm"
          >
            {showQR ? '隐藏二维码' : '显示二维码'}
          </button>
          <p className="text-xs text-gray-500 mt-2">
            使用此二维码可在院内自助设备完成各项操作
          </p>
        </div>

        {/* 下次就诊信息 */}
        <div className="glass-card rounded-2xl p-4">
          <h3 className="font-semibold text-gray-800 mb-3 flex items-center">
            <Calendar size={20} className="mr-2 text-primary" />
            下次就诊
          </h3>
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-blue-800">{patientInfo.nextAppointment}</p>
                <p className="text-blue-600 text-sm">{patientInfo.department} · {patientInfo.doctor}</p>
                <p className="text-blue-500 text-xs mt-1">门诊楼3楼 · 建议提前30分钟到达</p>
              </div>
              <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                导航
              </button>
            </div>
          </div>
        </div>

        {/* 就诊记录 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-gray-800 flex items-center">
              <Clock size={20} className="mr-2 text-primary" />
              就诊记录
            </h3>
            <button className="text-primary text-sm font-medium">查看全部</button>
          </div>
          
          <div className="space-y-3">
            {recentAppointments.map((appointment) => (
              <div key={appointment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    appointment.status === 'upcoming' ? 'bg-blue-500' : 'bg-green-500'
                  }`}></div>
                  <div>
                    <p className="font-medium text-gray-800 text-sm">
                      {appointment.date} {appointment.time}
                    </p>
                    <p className="text-gray-600 text-xs">
                      {appointment.department} · {appointment.doctor}
                    </p>
                    <p className="text-gray-500 text-xs">{appointment.location}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    appointment.status === 'upcoming' 
                      ? 'bg-blue-100 text-blue-600' 
                      : 'bg-green-100 text-green-600'
                  }`}>
                    {appointment.status === 'upcoming' ? '待就诊' : '已完成'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 快捷服务 */}
        <div className="glass-card rounded-2xl p-4">
          <h3 className="font-semibold text-gray-800 mb-4">快捷服务</h3>
          <div className="grid grid-cols-2 gap-3">
            {quickServices.map((service, index) => {
              const Icon = service.icon;
              return (
                <button
                  key={index}
                  className={`flex items-center p-4 rounded-xl ${service.color} transition-transform active:scale-95`}
                >
                  <Icon size={20} className="mr-3" />
                  <span className="font-medium text-sm">{service.label}</span>
                  <ChevronRight size={16} className="ml-auto opacity-60" />
                </button>
              );
            })}
          </div>
        </div>

        {/* 医院信息 */}
        <div className="glass-card rounded-2xl p-4">
          <h3 className="font-semibold text-gray-800 mb-3">医院信息</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <MapPin size={16} className="text-gray-500 mr-3" />
              <div>
                <p className="text-gray-800 text-sm font-medium">四川大学华西医院</p>
                <p className="text-gray-600 text-xs">成都市武侯区国学巷37号</p>
              </div>
            </div>
            <div className="flex items-center">
              <Phone size={16} className="text-gray-500 mr-3" />
              <div>
                <p className="text-gray-800 text-sm font-medium">咨询电话</p>
                <p className="text-gray-600 text-xs">028-85422286</p>
              </div>
            </div>
            <div className="flex items-center">
              <Clock size={16} className="text-gray-500 mr-3" />
              <div>
                <p className="text-gray-800 text-sm font-medium">门诊时间</p>
                <p className="text-gray-600 text-xs">周一至周日 08:00-17:30</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardPage;
