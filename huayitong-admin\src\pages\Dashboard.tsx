import React, { useState } from 'react';
import { 
  Users, 
  AlertTriangle, 
  CheckCircle, 
  Calendar,
  Activity,
  TrendingUp,
  Clock,
  Heart,
  Pill,
  FileText,
  Bell
} from 'lucide-react';
import { mockStats, mockPatients, mockTasks, mockConfirmationData } from '../data/mockData';
import ConfirmationModal from '../components/common/ConfirmationModal';

const Dashboard: React.FC = () => {
  const [showConfirmation, setShowConfirmation] = useState(false);

  const urgentPatients = mockPatients.filter(p => 
    p.alerts.some(alert => alert.type === 'urgent')
  );

  const pendingTasks = mockTasks.filter(task => task.status === 'pending');
  const urgentTasks = pendingTasks.filter(task => task.urgency === 'high');

  const stats = [
    {
      title: '今日患者',
      value: mockStats.todayPatients,
      unit: '人',
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      trend: '+12%'
    },
    {
      title: '紧急提醒',
      value: mockStats.urgentAlerts,
      unit: '项',
      icon: AlertTriangle,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      trend: '-5%'
    },
    {
      title: '待办任务',
      value: mockStats.pendingTasks,
      unit: '个',
      icon: FileText,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      trend: '+8%'
    },
    {
      title: '已完成',
      value: mockStats.completedTasks,
      unit: '个',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      trend: '+15%'
    }
  ];

  const handleConfirmAction = (action: string, customNote?: string) => {
    console.log('确认操作:', action, customNote);
    setShowConfirmation(false);
    // 这里处理确认后的逻辑
  };

  return (
    <div className="p-6 h-full overflow-y-auto">
      {/* 顶部欢迎区域 */}
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">
              早上好，李教授 👋
            </h1>
            <p className="text-gray-600 mt-1">
              今天是 2025年6月20日，您有 {mockStats.todayPatients} 位患者预约
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => setShowConfirmation(true)}
              className="relative p-2 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors"
            >
              <Bell size={20} />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"></span>
            </button>
            <div className="text-right">
              <p className="text-sm font-medium text-gray-800">心内科</p>
              <p className="text-xs text-gray-500">主任医师</p>
            </div>
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="glass-card rounded-lg p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <div className="flex items-baseline mt-2">
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <span className="ml-1 text-sm text-gray-500">{stat.unit}</span>
                  </div>
                  <div className="flex items-center mt-2">
                    <TrendingUp size={12} className="text-green-500 mr-1" />
                    <span className="text-xs text-green-600">{stat.trend}</span>
                    <span className="text-xs text-gray-500 ml-1">vs 昨日</span>
                  </div>
                </div>
                <div className={`w-12 h-12 ${stat.bgColor} rounded-lg flex items-center justify-center`}>
                  <Icon size={24} className={stat.color} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* 紧急患者提醒 */}
        <div className="glass-card rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              <AlertTriangle size={20} className="text-red-600 mr-2" />
              紧急患者提醒
            </h2>
            <span className="text-sm text-gray-500">{urgentPatients.length} 位患者</span>
          </div>
          
          <div className="space-y-3">
            {urgentPatients.slice(0, 3).map((patient) => (
              <div key={patient.id} className="urgent-bg rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-red-700">
                        {patient.name[0]}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-red-800">{patient.name}</p>
                      <p className="text-sm text-red-600">{patient.diagnosis}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-red-600">
                      {patient.alerts[0]?.timestamp.split(' ')[1]}
                    </p>
                    <button 
                      onClick={() => setShowConfirmation(true)}
                      className="text-xs bg-red-600 text-white px-2 py-1 rounded mt-1 hover:bg-red-700 transition-colors"
                    >
                      处理
                    </button>
                  </div>
                </div>
                <div className="mt-2">
                  <p className="text-sm text-red-700">
                    {patient.alerts[0]?.message}
                  </p>
                  <div className="flex items-center mt-2 space-x-4">
                    <div className="flex items-center">
                      <Heart size={12} className="text-red-500 mr-1" />
                      <span className="text-xs text-red-600">{patient.vitals.heartRate} bpm</span>
                    </div>
                    <div className="flex items-center">
                      <Activity size={12} className="text-red-500 mr-1" />
                      <span className="text-xs text-red-600">{patient.vitals.bloodPressure}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <button className="w-full mt-4 text-center text-sm text-red-600 hover:text-red-800 font-medium">
            查看所有紧急患者 ({urgentPatients.length})
          </button>
        </div>

        {/* 今日任务概览 */}
        <div className="glass-card rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              <FileText size={20} className="text-blue-600 mr-2" />
              今日任务概览
            </h2>
            <span className="text-sm text-gray-500">{pendingTasks.length} 个待办</span>
          </div>
          
          <div className="space-y-3">
            {urgentTasks.slice(0, 4).map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    task.type === 'medication' ? 'bg-purple-100' :
                    task.type === 'examination' ? 'bg-green-100' :
                    task.type === 'followup' ? 'bg-orange-100' :
                    'bg-blue-100'
                  }`}>
                    {task.type === 'medication' ? (
                      <Pill size={14} className="text-purple-600" />
                    ) : task.type === 'examination' ? (
                      <Activity size={14} className="text-green-600" />
                    ) : task.type === 'followup' ? (
                      <Calendar size={14} className="text-orange-600" />
                    ) : (
                      <FileText size={14} className="text-blue-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 text-sm">{task.title}</p>
                    <p className="text-xs text-gray-600">{task.patientName}</p>
                  </div>
                </div>
                <div className="text-right">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    task.urgency === 'high' ? 'bg-red-100 text-red-600' :
                    task.urgency === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                    'bg-blue-100 text-blue-600'
                  }`}>
                    {task.urgency === 'high' ? '紧急' :
                     task.urgency === 'medium' ? '重要' : '一般'}
                  </span>
                  <p className="text-xs text-gray-500 mt-1">
                    {new Date(task.dueDate).toLocaleDateString('zh-CN')}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          <button className="w-full mt-4 text-center text-sm text-blue-600 hover:text-blue-800 font-medium">
            查看所有任务 ({pendingTasks.length})
          </button>
        </div>

        {/* 今日排班 */}
        <div className="glass-card rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              <Calendar size={20} className="text-green-600 mr-2" />
              今日排班
            </h2>
            <span className="text-sm text-gray-500">8 位患者</span>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
              <div>
                <p className="font-medium text-green-800">上午门诊</p>
                <p className="text-sm text-green-600">09:00 - 12:00</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-green-700">5</p>
                <p className="text-xs text-green-600">位患者</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <div>
                <p className="font-medium text-blue-800">下午门诊</p>
                <p className="text-sm text-blue-600">14:00 - 17:00</p>
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-blue-700">3</p>
                <p className="text-xs text-blue-600">位患者</p>
              </div>
            </div>
            
            <div className="border-t pt-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">下一位患者</span>
                <span className="text-gray-800 font-medium">张三 (09:30)</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">主诉：胸闷气短，血压控制不佳</p>
            </div>
          </div>
        </div>

        {/* 系统状态 */}
        <div className="glass-card rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              <Activity size={20} className="text-purple-600 mr-2" />
              系统状态
            </h2>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-sm text-green-600">运行正常</span>
            </div>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">患者满意度</span>
              <span className="text-sm font-medium text-gray-800">{mockStats.patientSatisfaction}/5.0</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">平均响应时间</span>
              <span className="text-sm font-medium text-gray-800">{mockStats.responseTime}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">紧急处理率</span>
              <span className="text-sm font-medium text-gray-800">{mockStats.emergencyRate}</span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">本周预约</span>
              <span className="text-sm font-medium text-gray-800">{mockStats.weeklyAppointments} 人次</span>
            </div>
          </div>
          
          <div className="mt-4 pt-4 border-t">
            <div className="flex items-center">
              <Clock size={14} className="text-gray-400 mr-2" />
              <span className="text-xs text-gray-500">
                上次同步: 2025-06-20 06:53
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 确认弹窗 */}
      <ConfirmationModal
        isOpen={showConfirmation}
        data={mockConfirmationData}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmAction}
      />
    </div>
  );
};

export default Dashboard;
