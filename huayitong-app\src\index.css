@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 8px;
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 180 100% 35.3%;
    --primary-foreground: 0 0% 98%;
    --secondary: 11 100% 66.3%;
    --secondary-foreground: 0 0% 9%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 180 53% 97%;
    --accent-foreground: 180 100% 35.3%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 180 100% 35.3%;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    @apply bg-gradient-to-br from-accent via-white to-primary-50;
    min-height: 100vh;
  }

  /* 华医通专用样式 */
  .chat-bubble {
    @apply rounded-2xl px-4 py-3 max-w-[280px] shadow-sm;
  }

  .chat-bubble-user {
    @apply bg-primary text-primary-foreground ml-auto;
  }

  .chat-bubble-ai {
    @apply bg-white text-gray-800 border border-gray-100;
  }

  .glass-card {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg;
  }

  .mobile-container {
    @apply max-w-md mx-auto min-h-screen bg-white relative;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-primary-50 via-white to-accent;
  }
}

img {
  object-position: top;
}