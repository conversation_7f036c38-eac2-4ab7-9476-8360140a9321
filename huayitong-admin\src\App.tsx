import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import MainLayout from './components/layout/MainLayout';
import Dashboard from './pages/Dashboard';
import CalendarPage from './pages/CalendarPage';
import PatientsPage from './pages/PatientsPage';
import TasksPage from './pages/TasksPage';
import EducationPage from './pages/EducationPage';
import GuidelinesPage from './pages/GuidelinesPage';
import ResearchPage from './pages/ResearchPage';
import AnalyticsPage from './pages/AnalyticsPage';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<MainLayout />}>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<Dashboard />} />
            <Route path="calendar" element={<CalendarPage />} />
            <Route path="patients" element={<PatientsPage />} />
            <Route path="tasks" element={<TasksPage />} />
            <Route path="education" element={<EducationPage />} />
            <Route path="guidelines" element={<GuidelinesPage />} />
            <Route path="research" element={<ResearchPage />} />
            <Route path="analytics" element={<AnalyticsPage />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
}

export default App;
