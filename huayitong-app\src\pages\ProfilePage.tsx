import React, { useState } from 'react';
import { 
  <PERSON><PERSON>s, 
  Bell, 
  HelpCircle,
  User,
  Brain,
  Sliders,
  Clock,
  MessageCircle,
  Target,
  Shield,
  Crown,
  ChevronRight,
  Phone,
  Heart,
  Star
} from 'lucide-react';

interface SettingItem {
  id: string;
  name: string;
  description: string;
  type: 'toggle' | 'select' | 'button';
  value?: boolean | string;
  options?: string[];
}

const ProfilePage: React.FC = () => {
  const [aiSettings, setAiSettings] = useState<SettingItem[]>([
    {
      id: 'voice-style',
      name: '对话风格',
      description: '选择AI助手的交流方式',
      type: 'select',
      value: '温和',
      options: ['温和', '专业', '亲切', '简洁']
    },
    {
      id: 'recommendation-precision',
      name: '推荐算法精度',
      description: '调整个性化推荐的准确性',
      type: 'select',
      value: '精准',
      options: ['保守', '适中', '精准', '积极']
    },
    {
      id: 'reminder-frequency',
      name: '提醒频率',
      description: '设置健康提醒的频次',
      type: 'select',
      value: '适中',
      options: ['低频', '适中', '高频']
    }
  ]);

  const [notifications, setNotifications] = useState<SettingItem[]>([
    {
      id: 'medication-reminder',
      name: '用药提醒',
      description: '按时提醒服药时间',
      type: 'toggle',
      value: true
    },
    {
      id: 'appointment-reminder',
      name: '复诊提醒',
      description: '预约就诊时间提醒',
      type: 'toggle',
      value: true
    },
    {
      id: 'health-news',
      name: '健康资讯',
      description: '推送相关健康知识',
      type: 'toggle',
      value: false
    },
    {
      id: 'exercise-reminder',
      name: '运动提醒',
      description: '日常锻炼计划提醒',
      type: 'toggle',
      value: true
    }
  ]);

  const userInfo = {
    name: '张三',
    id: '135****0271',
    department: '心内科患者',
    avatar: '👤',
    membershipLevel: 'Premium',
    membershipExpiry: '2025-12-31'
  };

  const handleAISettingChange = (id: string, value: string) => {
    setAiSettings(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value } : setting
    ));
  };

  const handleNotificationToggle = (id: string) => {
    setNotifications(prev => prev.map(setting => 
      setting.id === id ? { ...setting, value: !setting.value } : setting
    ));
  };

  const menuItems = [
    {
      icon: Heart,
      title: '紧急联系人',
      subtitle: '管理紧急情况联系人',
      color: 'text-red-500',
      bgColor: 'bg-red-50'
    },
    {
      icon: Shield,
      title: '隐私设置',
      subtitle: '数据共享和使用控制',
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      icon: Target,
      title: '工作流配置',
      subtitle: '个性化就医流程设置',
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      icon: Phone,
      title: '客服中心',
      subtitle: '联系华医通客服',
      color: 'text-purple-500',
      bgColor: 'bg-purple-50'
    }
  ];

  return (
    <div className="mobile-container gradient-bg">
      {/* 顶部导航 */}
      <header className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-white/20">
        <h1 className="text-xl font-bold text-gray-800">个性化配置中心</h1>
        <div className="flex space-x-3">
          <button className="p-2 rounded-full bg-gray-100 text-gray-600">
            <Bell size={20} />
          </button>
          <button className="p-2 rounded-full bg-gray-100 text-gray-600">
            <HelpCircle size={20} />
          </button>
        </div>
      </header>

      {/* 内容区域 */}
      <div className="px-4 py-6 pb-20 space-y-6">
        
        {/* 用户信息卡片 */}
        <div className="glass-card rounded-2xl p-6">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center text-2xl text-white">
              {userInfo.avatar}
            </div>
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h2 className="text-xl font-bold text-gray-800">{userInfo.name}</h2>
                <div className="flex items-center bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs">
                  <Crown size={12} className="mr-1" />
                  {userInfo.membershipLevel}
                </div>
              </div>
              <p className="text-gray-600 text-sm">ID: {userInfo.id}</p>
              <p className="text-gray-500 text-sm">{userInfo.department}</p>
            </div>
            <button className="p-2 text-gray-400">
              <ChevronRight size={20} />
            </button>
          </div>
        </div>

        {/* AI助手个性化设置 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Brain size={20} className="mr-2 text-primary" />
            AI助手个性化设置
          </h2>
          
          <div className="space-y-4">
            {aiSettings.map((setting) => (
              <div key={setting.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                <div className="flex-1">
                  <p className="font-medium text-gray-800 text-sm">{setting.name}</p>
                  <p className="text-gray-600 text-xs mt-1">{setting.description}</p>
                </div>
                <div>
                  {setting.type === 'select' && (
                    <select
                      value={setting.value as string}
                      onChange={(e) => handleAISettingChange(setting.id, e.target.value)}
                      className="text-sm bg-gray-100 rounded-lg px-3 py-2 border-none outline-none min-w-20"
                    >
                      {setting.options?.map((option) => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-center">
              <Settings size={16} className="text-blue-600 mr-2" />
              <p className="text-blue-800 text-sm font-medium">个性化设置说明</p>
            </div>
            <p className="text-blue-600 text-xs mt-1">
              AI助手会根据您的设置调整服务方式，提供更符合您习惯的医疗咨询体验。
            </p>
          </div>
        </div>

        {/* 通知偏好设置 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Bell size={20} className="mr-2 text-primary" />
            通知偏好
          </h2>
          
          <div className="space-y-4">
            {notifications.map((notification) => (
              <div key={notification.id} className="flex items-center justify-between py-3">
                <div className="flex-1">
                  <p className="font-medium text-gray-800 text-sm">{notification.name}</p>
                  <p className="text-gray-600 text-xs mt-1">{notification.description}</p>
                </div>
                <button
                  onClick={() => handleNotificationToggle(notification.id)}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    notification.value ? 'bg-primary' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      notification.value ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* 订阅服务 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Crown size={20} className="mr-2 text-yellow-500" />
            订阅服务
          </h2>
          
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4">
            <div className="flex items-center justify-between mb-3">
              <div>
                <p className="font-bold text-yellow-800">华医通Premium会员</p>
                <p className="text-yellow-600 text-sm">专享AI医疗助手服务</p>
              </div>
              <div className="flex items-center bg-yellow-500 text-white px-3 py-1 rounded-full text-xs">
                <Star size={12} className="mr-1" />
                活跃中
              </div>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex items-center text-sm text-yellow-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                无限AI智能助手对话
              </div>
              <div className="flex items-center text-sm text-yellow-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                个性化健康管理服务
              </div>
              <div className="flex items-center text-sm text-yellow-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                专家优先预约通道
              </div>
              <div className="flex items-center text-sm text-yellow-700">
                <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                24/7紧急健康咨询
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <p className="text-yellow-600 text-sm">
                到期时间: {userInfo.membershipExpiry}
              </p>
              <button className="bg-yellow-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                续费
              </button>
            </div>
          </div>

          <div className="mt-4 p-3 bg-gray-50 rounded-xl">
            <p className="text-gray-700 text-sm font-medium mb-2">💳 定价方案</p>
            <div className="space-y-1 text-sm text-gray-600">
              <p>• 月度订阅：¥29.9/月</p>
              <p>• 年度订阅：¥299/年（相当于8.3折）</p>
              <p>• 家庭套餐：¥499/年（支持4位家庭成员）</p>
            </div>
          </div>
        </div>

        {/* 工作流配置和其他功能 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Sliders size={20} className="mr-2 text-primary" />
            更多配置
          </h2>
          
          <div className="space-y-3">
            {menuItems.map((item, index) => {
              const Icon = item.icon;
              return (
                <button
                  key={index}
                  className="w-full flex items-center p-3 rounded-xl bg-gray-50 hover:bg-gray-100 transition-colors"
                >
                  <div className={`w-10 h-10 ${item.bgColor} rounded-full flex items-center justify-center mr-3`}>
                    <Icon size={18} className={item.color} />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="font-medium text-gray-800 text-sm">{item.title}</p>
                    <p className="text-gray-600 text-xs">{item.subtitle}</p>
                  </div>
                  <ChevronRight size={16} className="text-gray-400" />
                </button>
              );
            })}
          </div>
        </div>

        {/* 关于华医通 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-3">关于华医通</h2>
          <div className="text-center space-y-2">
            <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto">
              <span className="text-white text-2xl">🏥</span>
            </div>
            <p className="font-medium text-gray-800">华医通 v2.0</p>
            <p className="text-gray-600 text-sm">四川大学华西医院官方App</p>
            <p className="text-gray-500 text-xs">以患者为中心的智能医疗服务</p>
            
            <div className="flex justify-center space-x-4 pt-4">
              <button className="text-primary text-sm font-medium">隐私政策</button>
              <span className="text-gray-300">|</span>
              <button className="text-primary text-sm font-medium">服务条款</button>
              <span className="text-gray-300">|</span>
              <button className="text-primary text-sm font-medium">意见反馈</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
