import React, { useState, useRef, useEffect } from 'react';
import { 
  Send, 
  Mic, 
  Bot,
  AlertTriangle,
  Clock,
  User,
  Zap
} from 'lucide-react';

interface Message {
  id: string;
  text: string;
  isAI: boolean;
  timestamp: Date;
  type?: 'normal' | 'suggestion' | 'alert';
}

interface QuickAction {
  id: string;
  label: string;
  icon: any;
  command: string;
}

const AIChatPanel: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: '您好，李教授！我是华医通智能助手，为您提供医疗决策支持。今天有什么可以帮助您的吗？',
      isAI: true,
      timestamp: new Date(),
      type: 'normal'
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickActions: QuickAction[] = [
    { id: '1', label: '患者异常', icon: AlertTriangle, command: '查看异常患者' },
    { id: '2', label: '今日排班', icon: Clock, command: '显示今日排班' },
    { id: '3', label: '药物查询', icon: Zap, command: '查询药物信息' },
    { id: '4', label: '诊疗指南', icon: Bot, command: '搜索诊疗指南' }
  ];

  const handleSendMessage = (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      isAI: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = generateAIResponse(messageText);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse.text,
        isAI: true,
        timestamp: new Date(),
        type: aiResponse.type
      };
      setMessages(prev => [...prev, aiMessage]);
    }, 1000);
  };

  const generateAIResponse = (userMessage: string): { text: string; type: 'normal' | 'suggestion' | 'alert' } => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('异常') || lowerMessage.includes('患者')) {
      return {
        text: '发现3位患者有异常情况：张三血压偏高(160/95)，李四血糖异常(8.2)，王五心率不齐。建议优先处理张三的高血压问题，是否需要调整用药方案？',
        type: 'alert'
      };
    } else if (lowerMessage.includes('排班') || lowerMessage.includes('今日')) {
      return {
        text: '今日您的排班：上午9:00-12:00有8位患者预约，下午14:00-17:00有6位患者。下一位患者是张三，主诉胸闷气短，建议重点关注心电图结果。',
        type: 'normal'
      };
    } else if (lowerMessage.includes('药物') || lowerMessage.includes('查询')) {
      return {
        text: '为您推荐查询：阿司匹林与氯吡格雷联用的出血风险评估。根据患者年龄和既往史，建议调整剂量至75mg qd，并定期监测凝血功能。',
        type: 'suggestion'
      };
    } else if (lowerMessage.includes('指南') || lowerMessage.includes('诊疗')) {
      return {
        text: '已为您找到最新的《急性心肌梗死诊疗指南(2023版)》，关键更新：STEMI患者首次医疗接触到球囊扩张时间应≤90分钟。是否需要查看详细内容？',
        type: 'suggestion'
      };
    } else {
      return {
        text: '我正在分析您的问题。作为您的智能医疗助手，我可以帮您：1)监测患者异常 2)提供诊疗建议 3)查询医学资料 4)协助排班管理。请告诉我您需要什么帮助？',
        type: 'normal'
      };
    }
  };

  const handleQuickAction = (command: string) => {
    handleSendMessage(command);
  };

  const handleVoiceInput = () => {
    setIsRecording(!isRecording);
    // 这里实现语音输入逻辑
    if (!isRecording) {
      // 模拟语音输入
      setTimeout(() => {
        setIsRecording(false);
        handleSendMessage('查看异常患者情况');
      }, 3000);
    }
  };

  const getMessageStyle = (type?: string) => {
    switch (type) {
      case 'alert':
        return 'border-l-4 border-red-500 bg-red-50';
      case 'suggestion':
        return 'border-l-4 border-blue-500 bg-blue-50';
      default:
        return '';
    }
  };

  return (
    <div className="ai-chat-panel flex flex-col h-full">
      {/* 顶部标题 */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
            <Bot size={18} className="text-white" />
          </div>
          <div>
            <h2 className="font-semibold text-gray-800">医疗AI助手</h2>
            <p className="text-xs text-gray-500">智能决策支持</p>
          </div>
          <div className="ml-auto">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* 快捷操作 */}
      <div className="p-3 border-b border-gray-200 bg-gray-50">
        <p className="text-xs text-gray-600 mb-2 font-medium">🚀 快捷指令</p>
        <div className="grid grid-cols-2 gap-2">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <button
                key={action.id}
                onClick={() => handleQuickAction(action.command)}
                className="flex items-center p-2 text-xs bg-white border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
              >
                <Icon size={12} className="mr-1.5 text-gray-500" />
                <span className="text-gray-700 truncate">{action.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* 对话区域 */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.isAI ? 'justify-start' : 'justify-end'}`}
          >
            <div className="max-w-[280px]">
              {message.isAI && (
                <div className="flex items-center mb-1">
                  <Bot size={12} className="text-primary mr-1" />
                  <span className="text-xs text-gray-500">AI助手</span>
                </div>
              )}
              <div
                className={`chat-bubble ${
                  message.isAI ? 'chat-bubble-ai' : 'chat-bubble-user'
                } ${message.isAI ? getMessageStyle(message.type) : ''}`}
              >
                <p className="text-sm leading-relaxed">{message.text}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
              </div>
            </div>
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <div className="flex items-center space-x-2">
          <button
            onClick={handleVoiceInput}
            className={`p-2 rounded-lg transition-colors ${
              isRecording 
                ? 'bg-red-500 text-white animate-pulse' 
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
            }`}
          >
            <Mic size={16} />
          </button>
          <input
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder={isRecording ? '正在录音...' : '询问医疗问题或输入指令'}
            className="flex-1 px-3 py-2 bg-gray-50 rounded-lg border-none outline-none text-sm"
            disabled={isRecording}
          />
          <button
            onClick={() => handleSendMessage()}
            disabled={!inputText.trim() || isRecording}
            className="p-2 bg-primary text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors hover:bg-primary-600"
          >
            <Send size={16} />
          </button>
        </div>
        
        {/* 输入提示 */}
        <div className="mt-2 text-xs text-gray-500">
          💡 提示：输入"异常患者"、"今日排班"、"药物查询"等获取快速帮助
        </div>
      </div>
    </div>
  );
};

export default AIChatPanel;
