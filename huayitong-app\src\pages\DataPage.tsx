import React, { useState } from 'react';
import { 
  Heart, 
  Activity, 
  Thermometer,
  Droplets,
  TrendingUp,
  Calendar,
  FileText,
  AlertTriangle,
  ChevronRight,
  BarChart3,
  Smartphone
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface VitalSign {
  id: string;
  name: string;
  value: string;
  unit: string;
  status: 'normal' | 'warning' | 'danger';
  icon: any;
  color: string;
  trend: 'up' | 'down' | 'stable';
}

interface MedicalRecord {
  id: string;
  date: string;
  department: string;
  doctor: string;
  diagnosis: string;
  type: 'consultation' | 'examination';
}

const DataPage: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('week');
  const [healthMood, setHealthMood] = useState('😊');

  // 生理指征数据
  const vitalSigns: VitalSign[] = [
    {
      id: '1',
      name: '心率',
      value: '72',
      unit: 'bpm',
      status: 'normal',
      icon: Heart,
      color: 'text-red-500',
      trend: 'stable'
    },
    {
      id: '2',
      name: '血压',
      value: '128/82',
      unit: 'mmHg',
      status: 'warning',
      icon: Activity,
      color: 'text-orange-500',
      trend: 'up'
    },
    {
      id: '3',
      name: '体温',
      value: '36.5',
      unit: '°C',
      status: 'normal',
      icon: Thermometer,
      color: 'text-blue-500',
      trend: 'stable'
    },
    {
      id: '4',
      name: '血糖',
      value: '5.8',
      unit: 'mmol/L',
      status: 'warning',
      icon: Droplets,
      color: 'text-purple-500',
      trend: 'up'
    }
  ];

  // 趋势图数据
  const chartData = [
    { name: '6/14', 收缩压: 125, 舒张压: 80 },
    { name: '6/15', 收缩压: 128, 舒张压: 82 },
    { name: '6/16', 收缩压: 122, 舒张压: 78 },
    { name: '6/17', 收缩压: 130, 舒张压: 85 },
    { name: '6/18', 收缩压: 126, 舒张压: 79 },
    { name: '6/19', 收缩压: 124, 舒张压: 81 },
    { name: '6/20', 收缩压: 128, 舒张压: 82 }
  ];

  // 就诊记录
  const medicalRecords: MedicalRecord[] = [
    {
      id: '1',
      date: '2025-06-15',
      department: '心内科',
      doctor: '李教授',
      diagnosis: '高血压（控制良好）',
      type: 'consultation'
    },
    {
      id: '2',
      date: '2025-06-10',
      department: '心内科',
      doctor: '李教授',
      diagnosis: '心电图检查',
      type: 'examination'
    },
    {
      id: '3',
      date: '2025-06-01',
      department: '心内科',
      doctor: '李教授',
      diagnosis: '血常规检查',
      type: 'examination'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-green-600 bg-green-50 border-green-200';
      case 'warning': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'danger': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return '↗️';
      case 'down': return '↘️';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const moodOptions = ['😢', '😕', '😐', '😊', '😄'];

  return (
    <div className="mobile-container gradient-bg">
      {/* 顶部导航 */}
      <header className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-white/20">
        <h1 className="text-xl font-bold text-gray-800">数字孪生健康档案</h1>
        <button className="text-primary text-sm font-medium">
          同步数据
        </button>
      </header>

      {/* 内容区域 */}
      <div className="px-4 py-6 pb-20 space-y-6">
        
        {/* 今日健康状态 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">今日健康状态</h2>
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-600 text-sm mb-2">我今天感觉</p>
              <div className="flex space-x-3">
                {moodOptions.map((mood, index) => (
                  <button
                    key={index}
                    onClick={() => setHealthMood(mood)}
                    className={`text-2xl p-2 rounded-full transition-all ${
                      healthMood === mood ? 'bg-primary/20 scale-125' : 'hover:bg-gray-100'
                    }`}
                  >
                    {mood}
                  </button>
                ))}
              </div>
            </div>
            <div className="text-center">
              <div className="text-4xl mb-2">{healthMood}</div>
              <p className="text-sm text-gray-600">状态良好</p>
            </div>
          </div>
        </div>

        {/* 实时生理指征 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              📊 实时生理指征
            </h2>
            <div className="flex items-center text-sm text-gray-500">
              <Smartphone size={14} className="mr-1" />
              智能设备连接
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            {vitalSigns.map((sign) => {
              const Icon = sign.icon;
              return (
                <div
                  key={sign.id}
                  className={`p-4 rounded-xl border-2 ${getStatusColor(sign.status)}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <Icon size={18} className={sign.color} />
                    <span className="text-xs">{getTrendIcon(sign.trend)}</span>
                  </div>
                  <p className="text-xs text-gray-600 mb-1">{sign.name}</p>
                  <div className="flex items-baseline">
                    <span className="text-lg font-bold text-gray-800">{sign.value}</span>
                    <span className="text-xs text-gray-500 ml-1">{sign.unit}</span>
                  </div>
                  <div className="mt-2">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      sign.status === 'normal' ? 'bg-green-100 text-green-600' :
                      sign.status === 'warning' ? 'bg-orange-100 text-orange-600' :
                      'bg-red-100 text-red-600'
                    }`}>
                      {sign.status === 'normal' ? '正常' :
                       sign.status === 'warning' ? '注意' : '异常'}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 健康趋势图表 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              📈 健康趋势分析
            </h2>
            <select 
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="text-sm bg-gray-100 rounded-lg px-2 py-1 border-none outline-none"
            >
              <option value="week">过去7天</option>
              <option value="month">过去30天</option>
              <option value="year">过去一年</option>
            </select>
          </div>
          
          <div className="bg-white rounded-xl p-4 border border-gray-100">
            <p className="text-sm text-gray-600 mb-3">血压变化趋势</p>
            <ResponsiveContainer width="100%" height={200}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="name" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#888' }}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#888' }}
                />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'white', 
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '12px'
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="收缩压" 
                  stroke="#00B4A6" 
                  strokeWidth={2}
                  dot={{ fill: '#00B4A6', strokeWidth: 2, r: 4 }}
                />
                <Line 
                  type="monotone" 
                  dataKey="舒张压" 
                  stroke="#FF7B54" 
                  strokeWidth={2}
                  dot={{ fill: '#FF7B54', strokeWidth: 2, r: 4 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 异常提醒 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            <AlertTriangle size={20} className="mr-2 text-orange-500" />
            健康提醒
          </h2>
          <div className="space-y-3">
            <div className="bg-orange-50 border border-orange-200 rounded-xl p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-orange-800 text-sm">血压偏高</p>
                  <p className="text-orange-600 text-xs mt-1">
                    建议减少盐分摄入，增加有氧运动
                  </p>
                </div>
                <button className="text-orange-600 text-xs font-medium">详情</button>
              </div>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-yellow-800 text-sm">用药提醒</p>
                  <p className="text-yellow-600 text-xs mt-1">
                    距离下次服药还有2小时
                  </p>
                </div>
                <button className="text-yellow-600 text-xs font-medium">设置</button>
              </div>
            </div>
          </div>
        </div>

        {/* 就诊记录 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              🏥 就诊记录
            </h2>
            <button className="text-primary text-sm font-medium">查看全部</button>
          </div>
          
          <div className="space-y-3">
            {medicalRecords.map((record) => (
              <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    record.type === 'consultation' ? 'bg-blue-100' : 'bg-green-100'
                  }`}>
                    {record.type === 'consultation' ? (
                      <Calendar size={14} className="text-blue-600" />
                    ) : (
                      <FileText size={14} className="text-green-600" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 text-sm">{record.diagnosis}</p>
                    <p className="text-gray-600 text-xs">
                      {record.date} · {record.department} · {record.doctor}
                    </p>
                  </div>
                </div>
                <ChevronRight size={16} className="text-gray-400" />
              </div>
            ))}
          </div>
        </div>

        {/* 检查报告 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              🧪 检查报告
            </h2>
            <button className="text-primary text-sm font-medium">查看全部</button>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <Droplets size={14} className="text-red-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800 text-sm">血常规检查</p>
                  <p className="text-gray-600 text-xs">2025-06-15 · 各项指标正常</p>
                </div>
              </div>
              <div className="text-right">
                <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                  正常
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-xl">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <BarChart3 size={14} className="text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-800 text-sm">心电图检查</p>
                  <p className="text-gray-600 text-xs">2025-06-10 · 心律规整</p>
                </div>
              </div>
              <div className="text-right">
                <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full">
                  正常
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DataPage;
