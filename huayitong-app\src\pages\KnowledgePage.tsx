import React, { useState } from 'react';
import { 
  Search, 
  Bell, 
  CheckCircle2, 
  Clock, 
  Play,
  Star,
  TrendingUp,
  Users,
  Heart
} from 'lucide-react';

interface Task {
  id: string;
  name: string;
  completed: boolean;
  time?: string;
}

interface Article {
  id: string;
  title: string;
  type: 'video' | 'article';
  rating: number;
  views: string;
  image: string;
  category: string;
}

const KnowledgePage: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([
    { id: '1', name: '步行锻炼', completed: true },
    { id: '2', name: '服用降压药', completed: true },
    { id: '3', name: '血压测量', completed: false, time: '14:30' },
    { id: '4', name: '康复训练', completed: false, time: '16:00' },
    { id: '5', name: '睡前用药', completed: false, time: '21:00' }
  ]);

  const completedTasks = tasks.filter(task => task.completed).length;
  const totalTasks = tasks.length;

  const articles: Article[] = [
    {
      id: '1',
      title: '心脏康复运动指南：科学锻炼促进恢复',
      type: 'video',
      rating: 4.8,
      views: '2.1万',
      image: '/images/heart-exercise.jpg',
      category: '康复指导'
    },
    {
      id: '2',
      title: '高血压患者日常饮食注意事项',
      type: 'article',
      rating: 4.7,
      views: '1.8万',
      image: '/images/healthy-diet.jpg',
      category: '营养指导'
    },
    {
      id: '3',
      title: '术后护理要点：促进伤口愈合',
      type: 'video',
      rating: 4.9,
      views: '3.2万',
      image: '/images/wound-care.jpg',
      category: '术后护理'
    },
    {
      id: '4',
      title: '老年人预防跌倒的家居安全指南',
      type: 'article',
      rating: 4.6,
      views: '1.5万',
      image: '/images/home-safety.jpg',
      category: '安全防护'
    }
  ];

  const toggleTask = (taskId: string) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId ? { ...task, completed: !task.completed } : task
    ));
  };

  return (
    <div className="mobile-container gradient-bg">
      {/* 顶部导航 */}
      <header className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-white/20">
        <h1 className="text-xl font-bold text-gray-800">广场</h1>
        <div className="flex space-x-3">
          <button className="p-2 rounded-full bg-gray-100 text-gray-600">
            <Search size={20} />
          </button>
          <button className="p-2 rounded-full bg-gray-100 text-gray-600 relative">
            <Bell size={20} />
            <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
          </button>
        </div>
      </header>

      {/* 内容区域 */}
      <div className="px-4 py-6 pb-20 space-y-6">
        
        {/* 今日康复任务 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              🎯 今日康复任务
            </h2>
            <span className="text-sm text-gray-600">
              {completedTasks}/{totalTasks}完成
            </span>
          </div>
          
          {/* 进度条 */}
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-600 mb-2">
              <span>完成进度</span>
              <span>{Math.round((completedTasks / totalTasks) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${(completedTasks / totalTasks) * 100}%` }}
              ></div>
            </div>
          </div>

          {/* 任务列表 */}
          <div className="grid grid-cols-2 gap-3">
            {tasks.slice(0, 4).map((task) => (
              <button
                key={task.id}
                onClick={() => toggleTask(task.id)}
                className={`p-3 rounded-xl border-2 transition-all ${
                  task.completed
                    ? 'bg-green-50 border-green-200 text-green-700'
                    : 'bg-gray-50 border-gray-200 text-gray-600'
                }`}
              >
                <div className="flex items-center space-x-2">
                  {task.completed ? (
                    <CheckCircle2 size={16} className="text-green-600" />
                  ) : (
                    <Clock size={16} className="text-gray-400" />
                  )}
                  <span className="text-sm font-medium">{task.name}</span>
                </div>
                {task.time && !task.completed && (
                  <p className="text-xs text-gray-500 mt-1">{task.time}</p>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* 数字医嘱提醒 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
            💊 数字医嘱提醒
          </h2>
          <div className="bg-orange-50 border border-orange-200 rounded-xl p-4">
            <p className="text-orange-800 font-medium">高血压药物，饭后服用</p>
            <p className="text-orange-600 text-sm mt-1">下次用药: 今天 14:30</p>
            <div className="flex justify-between items-center mt-3">
              <span className="text-orange-700 text-sm">⏰ 30分钟后提醒</span>
              <button className="text-orange-600 text-sm font-medium">设置提醒</button>
            </div>
          </div>
        </div>

        {/* 个性化推荐 */}
        <div className="glass-card rounded-2xl p-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-800 flex items-center">
              🧠 个性化推荐
            </h2>
            <button className="text-primary text-sm font-medium">更多内容</button>
          </div>

          <div className="space-y-4">
            {articles.map((article) => (
              <div key={article.id} className="flex space-x-3 p-3 bg-gray-50 rounded-xl">
                <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0 flex items-center justify-center">
                  {article.type === 'video' ? (
                    <Play size={20} className="text-gray-500" />
                  ) : (
                    <span className="text-2xl">📖</span>
                  )}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-gray-800 text-sm leading-tight">
                    {article.title}
                  </p>
                  <div className="flex items-center space-x-3 mt-2">
                    <span className="flex items-center text-xs text-gray-500">
                      <Star size={12} className="text-yellow-500 mr-1" />
                      {article.rating}分
                    </span>
                    <span className="text-xs text-gray-500">
                      {article.views}观看
                    </span>
                    <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                      {article.category}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 科普专栏 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            📚 科普专栏
          </h2>
          
          <div className="grid grid-cols-2 gap-3">
            <button className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl text-left">
              <div className="text-2xl mb-2">❤️</div>
              <p className="font-medium text-blue-800 text-sm">高血压防治</p>
              <p className="text-blue-600 text-xs mt-1">15篇文章</p>
            </button>
            
            <button className="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl text-left">
              <div className="text-2xl mb-2">🏥</div>
              <p className="font-medium text-green-800 text-sm">术后护理</p>
              <p className="text-green-600 text-xs mt-1">12篇文章</p>
            </button>
            
            <button className="bg-gradient-to-br from-purple-50 to-purple-100 p-4 rounded-xl text-left">
              <div className="text-2xl mb-2">🧘</div>
              <p className="font-medium text-purple-800 text-sm">心理健康</p>
              <p className="text-purple-600 text-xs mt-1">18篇文章</p>
            </button>
            
            <button className="bg-gradient-to-br from-orange-50 to-orange-100 p-4 rounded-xl text-left">
              <div className="text-2xl mb-2">🍎</div>
              <p className="font-medium text-orange-800 text-sm">营养指导</p>
              <p className="text-orange-600 text-xs mt-1">21篇文章</p>
            </button>
          </div>
        </div>

        {/* 社区互动 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <Users size={20} className="mr-2" />
            患者交流社区
          </h2>
          
          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-xl">
              <p className="text-gray-800 text-sm">
                "术后第三天，按照医生建议开始慢走，感觉恢复得不错..."
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">心脏外科患者 · 2小时前</span>
                <div className="flex items-center space-x-3">
                  <span className="flex items-center text-xs text-gray-500">
                    <Heart size={12} className="mr-1" />
                    12
                  </span>
                  <span className="text-xs text-primary">回复</span>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-3 rounded-xl">
              <p className="text-gray-800 text-sm">
                "血压控制得很好，感谢华医通的用药提醒功能！"
              </p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-gray-500">高血压患者 · 5小时前</span>
                <div className="flex items-center space-x-3">
                  <span className="flex items-center text-xs text-gray-500">
                    <Heart size={12} className="mr-1" />
                    8
                  </span>
                  <span className="text-xs text-primary">回复</span>
                </div>
              </div>
            </div>
          </div>
          
          <button className="w-full mt-3 py-2 text-primary font-medium text-sm border border-primary rounded-lg">
            加入讨论
          </button>
        </div>

        {/* 健康商城 */}
        <div className="glass-card rounded-2xl p-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            🛒 健康商城
          </h2>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-gradient-to-br from-pink-50 to-red-100 p-4 rounded-xl">
              <div className="text-2xl mb-2">🩺</div>
              <p className="font-medium text-red-800 text-sm">家用医疗设备</p>
              <p className="text-red-600 text-xs mt-1">血压计、血糖仪等</p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-red-700">特惠价</span>
                <span className="text-xs bg-red-200 text-red-800 px-2 py-1 rounded-full">9折</span>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-green-50 to-emerald-100 p-4 rounded-xl">
              <div className="text-2xl mb-2">💊</div>
              <p className="font-medium text-green-800 text-sm">处方药品</p>
              <p className="text-green-600 text-xs mt-1">在线购药配送</p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-green-700">医保可用</span>
                <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full">快递</span>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-blue-50 to-sky-100 p-4 rounded-xl">
              <div className="text-2xl mb-2">🏃‍♂️</div>
              <p className="font-medium text-blue-800 text-sm">康复用品</p>
              <p className="text-blue-600 text-xs mt-1">康复器械、护理用品</p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-blue-700">品质保证</span>
                <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full">热销</span>
              </div>
            </div>
            
            <div className="bg-gradient-to-br from-yellow-50 to-amber-100 p-4 rounded-xl">
              <div className="text-2xl mb-2">🍯</div>
              <p className="font-medium text-amber-800 text-sm">营养保健</p>
              <p className="text-amber-600 text-xs mt-1">维生素、营养品</p>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-amber-700">医生推荐</span>
                <span className="text-xs bg-amber-200 text-amber-800 px-2 py-1 rounded-full">新品</span>
              </div>
            </div>
          </div>
          
          <button className="w-full mt-4 py-3 bg-primary text-white font-medium text-sm rounded-lg">
            进入健康商城
          </button>
        </div>
      </div>
    </div>
  );
};

export default KnowledgePage;
