@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 8px;
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 180 100% 35.3%;
    --primary-foreground: 0 0% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 180 53% 97%;
    --accent-foreground: 180 100% 35.3%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 180 100% 35.3%;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    @apply bg-gray-50;
    min-height: 100vh;
  }

  /* 华医通管理端专用样式 */
  .admin-layout {
    @apply min-h-screen bg-gray-50;
  }

  .sidebar {
    @apply bg-white border-r border-gray-200 shadow-sm;
    width: 200px;
    min-width: 200px;
  }

  .main-content {
    @apply flex-1 bg-white;
  }

  .ai-chat-panel {
    @apply bg-white border-l border-gray-200 shadow-sm;
    width: 320px;
    min-width: 320px;
  }

  .chat-bubble {
    @apply rounded-lg px-3 py-2 max-w-xs shadow-sm;
  }

  .chat-bubble-user {
    @apply bg-primary text-primary-foreground ml-auto;
  }

  .chat-bubble-ai {
    @apply bg-gray-100 text-gray-800;
  }

  .glass-card {
    @apply bg-white/95 backdrop-blur-sm border border-gray-200 shadow-sm;
  }

  .urgent-bg {
    @apply bg-red-50 border-red-200;
  }

  .important-bg {
    @apply bg-yellow-50 border-yellow-200;
  }

  .normal-bg {
    @apply bg-blue-50 border-blue-200;
  }

  .patient-card {
    @apply bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow;
  }

  .task-card {
    @apply bg-white border border-gray-200 rounded-lg p-3 cursor-pointer hover:bg-gray-50 transition-colors;
  }

  /* 响应式设计 */
  @media (max-width: 1199px) {
    .sidebar {
      width: 160px;
      min-width: 160px;
    }
    
    .ai-chat-panel {
      width: 280px;
      min-width: 280px;
    }
  }

  @media (max-width: 767px) {
    .admin-layout {
      @apply flex-col;
    }
    
    .sidebar {
      @apply w-full h-auto border-r-0 border-b border-gray-200;
    }
    
    .ai-chat-panel {
      @apply w-full border-l-0 border-t border-gray-200;
    }
  }
}

img {
  object-position: top;
}