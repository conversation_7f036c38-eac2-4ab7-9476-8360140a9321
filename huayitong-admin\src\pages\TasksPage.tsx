import React, { useState } from 'react';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  User,
  Calendar,
  Pill,
  Activity,
  MessageSquare,
  Filter,
  Plus,
  Search
} from 'lucide-react';
import { mockTasks, mockConfirmationData, Task } from '../data/mockData';
import ConfirmationModal from '../components/common/ConfirmationModal';

const TasksPage: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedUrgency, setSelectedUrgency] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  const filteredTasks = mockTasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.patientName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || task.status === selectedStatus;
    const matchesUrgency = selectedUrgency === 'all' || task.urgency === selectedUrgency;
    return matchesSearch && matchesStatus && matchesUrgency;
  });

  const taskStats = {
    total: mockTasks.length,
    pending: mockTasks.filter(t => t.status === 'pending').length,
    completed: mockTasks.filter(t => t.status === 'completed').length,
    overdue: mockTasks.filter(t => t.status === 'overdue').length,
    high: mockTasks.filter(t => t.urgency === 'high').length
  };

  const getTaskTypeConfig = (type: string) => {
    switch (type) {
      case 'medication':
        return { icon: Pill, label: '用药调整', color: 'text-purple-600', bg: 'bg-purple-50', border: 'border-purple-200' };
      case 'examination':
        return { icon: Activity, label: '检查预约', color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' };
      case 'followup':
        return { icon: Calendar, label: '随访安排', color: 'text-orange-600', bg: 'bg-orange-50', border: 'border-orange-200' };
      case 'lifestyle':
        return { icon: MessageSquare, label: '生活指导', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' };
      default:
        return { icon: FileText, label: '其他', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  const getUrgencyConfig = (urgency: string) => {
    switch (urgency) {
      case 'high':
        return { label: '紧急', color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200' };
      case 'medium':
        return { label: '重要', color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200' };
      case 'low':
        return { label: '一般', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' };
      default:
        return { label: '未知', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'completed':
        return { label: '已完成', color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200' };
      case 'pending':
        return { label: '待处理', color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200' };
      case 'overdue':
        return { label: '已逾期', color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200' };
      default:
        return { label: '未知', color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200' };
    }
  };

  const handleConfirmAction = (action: string, customNote?: string) => {
    console.log('确认操作:', action, customNote);
    setShowConfirmation(false);
    // 这里处理确认后的逻辑
  };

  const handleTaskAction = (task: Task) => {
    if (task.urgency === 'high' && task.status === 'pending') {
      setShowConfirmation(true);
    } else {
      // 处理其他任务操作
      console.log('处理任务:', task.title);
    }
  };

  return (
    <div className="h-full p-6 overflow-y-auto">
      {/* 顶部标题和统计 */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-800 flex items-center">
            <FileText size={28} className="mr-3 text-primary" />
            任务记录
          </h1>
          <button className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-600 transition-colors flex items-center">
            <Plus size={18} className="mr-2" />
            新建任务
          </button>
        </div>

        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总任务</p>
                <p className="text-2xl font-bold text-gray-900">{taskStats.total}</p>
              </div>
              <FileText size={24} className="text-gray-400" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">待处理</p>
                <p className="text-2xl font-bold text-blue-600">{taskStats.pending}</p>
              </div>
              <Clock size={24} className="text-blue-400" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">已完成</p>
                <p className="text-2xl font-bold text-green-600">{taskStats.completed}</p>
              </div>
              <CheckCircle size={24} className="text-green-400" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">已逾期</p>
                <p className="text-2xl font-bold text-red-600">{taskStats.overdue}</p>
              </div>
              <AlertTriangle size={24} className="text-red-400" />
            </div>
          </div>
          
          <div className="glass-card rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">紧急任务</p>
                <p className="text-2xl font-bold text-red-600">{taskStats.high}</p>
              </div>
              <AlertTriangle size={24} className="text-red-400" />
            </div>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="glass-card rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-4">
          {/* 搜索框 */}
          <div className="flex-1 relative">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索任务或患者姓名"
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            />
          </div>

          {/* 状态筛选 */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
          >
            <option value="all">全部状态</option>
            <option value="pending">待处理</option>
            <option value="completed">已完成</option>
            <option value="overdue">已逾期</option>
          </select>

          {/* 紧急程度筛选 */}
          <select
            value={selectedUrgency}
            onChange={(e) => setSelectedUrgency(e.target.value)}
            className="px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
          >
            <option value="all">全部优先级</option>
            <option value="high">紧急</option>
            <option value="medium">重要</option>
            <option value="low">一般</option>
          </select>
        </div>
      </div>

      {/* 任务列表 */}
      <div className="space-y-4">
        {filteredTasks.map((task) => {
          const typeConfig = getTaskTypeConfig(task.type);
          const urgencyConfig = getUrgencyConfig(task.urgency);
          const statusConfig = getStatusConfig(task.status);
          const TypeIcon = typeConfig.icon;

          return (
            <div key={task.id} className="glass-card rounded-lg p-6 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  {/* 任务类型图标 */}
                  <div className={`w-12 h-12 ${typeConfig.bg} rounded-lg flex items-center justify-center border ${typeConfig.border}`}>
                    <TypeIcon size={20} className={typeConfig.color} />
                  </div>

                  {/* 任务信息 */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="font-semibold text-gray-800">{task.title}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${typeConfig.bg} ${typeConfig.color} border ${typeConfig.border}`}>
                        {typeConfig.label}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${urgencyConfig.bg} ${urgencyConfig.color} border ${urgencyConfig.border}`}>
                        {urgencyConfig.label}
                      </span>
                    </div>

                    <p className="text-gray-700 mb-3">{task.description}</p>

                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <User size={14} className="mr-1" />
                        {task.patientName}
                      </div>
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        截止: {new Date(task.dueDate).toLocaleDateString('zh-CN')}
                      </div>
                      <div className="flex items-center">
                        <Clock size={14} className="mr-1" />
                        创建: {new Date(task.createdAt).toLocaleDateString('zh-CN')}
                      </div>
                    </div>
                  </div>
                </div>

                {/* 任务状态和操作 */}
                <div className="text-right">
                  <div className={`inline-flex items-center px-3 py-1 text-sm rounded-full mb-3 ${statusConfig.bg} ${statusConfig.color} border ${statusConfig.border}`}>
                    {task.status === 'completed' ? (
                      <CheckCircle size={14} className="mr-1" />
                    ) : task.status === 'overdue' ? (
                      <AlertTriangle size={14} className="mr-1" />
                    ) : (
                      <Clock size={14} className="mr-1" />
                    )}
                    {statusConfig.label}
                  </div>

                  {task.status === 'pending' && (
                    <div className="space-y-2">
                      <button
                        onClick={() => handleTaskAction(task)}
                        className={`block w-full px-4 py-2 text-sm rounded-lg transition-colors ${
                          task.urgency === 'high'
                            ? 'bg-red-600 text-white hover:bg-red-700'
                            : 'bg-primary text-white hover:bg-primary-600'
                        }`}
                      >
                        {task.urgency === 'high' ? '紧急处理' : '开始处理'}
                      </button>
                      <button className="block w-full px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                        查看详情
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* 空状态 */}
      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <FileText size={48} className="text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-500 mb-2">暂无任务</h3>
          <p className="text-gray-400">没有找到符合条件的任务</p>
        </div>
      )}

      {/* 确认弹窗 */}
      <ConfirmationModal
        isOpen={showConfirmation}
        data={mockConfirmationData}
        onClose={() => setShowConfirmation(false)}
        onConfirm={handleConfirmAction}
      />
    </div>
  );
};

export default TasksPage;
