import React from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import AIChatPanel from './AIChatPanel';

const MainLayout: React.FC = () => {
  return (
    <div className="admin-layout flex h-screen">
      {/* 左侧导航栏 */}
      <Sidebar />
      
      {/* 中间主内容区域 */}
      <main className="main-content flex-1 overflow-hidden">
        <Outlet />
      </main>
      
      {/* 右侧AI助手面板 */}
      <AIChatPanel />
    </div>
  );
};

export default MainLayout;
