import React, { useState } from 'react';
import { 
  X, 
  AlertTriangle, 
  Clock, 
  User,
  Activity,
  Pill,
  Calendar,
  MessageSquare,
  CheckCircle
} from 'lucide-react';

interface ConfirmationData {
  id: string;
  patientName: string;
  patientAge: number;
  patientDiagnosis: string;
  patientAvatar?: string;
  urgencyLevel: 'urgent' | 'important' | 'normal';
  actionType: 'medication' | 'examination' | 'lifestyle' | 'followup';
  anomalyDetails: string;
  currentValue: string;
  normalRange: string;
  aiSuggestion: string;
  alternativeOptions?: string[];
}

interface ConfirmationModalProps {
  isOpen: boolean;
  data?: ConfirmationData;
  onClose: () => void;
  onConfirm: (action: string, customNote?: string) => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  data,
  onClose,
  onConfirm
}) => {
  const [selectedAction, setSelectedAction] = useState('');
  const [customNote, setCustomNote] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen || !data) return null;

  const getUrgencyConfig = (level: string) => {
    switch (level) {
      case 'urgent':
        return {
          bgClass: 'urgent-bg',
          borderClass: 'border-red-500',
          textClass: 'text-red-700',
          iconClass: 'text-red-600',
          label: '紧急',
          icon: AlertTriangle
        };
      case 'important':
        return {
          bgClass: 'important-bg',
          borderClass: 'border-yellow-500',
          textClass: 'text-yellow-700',
          iconClass: 'text-yellow-600',
          label: '重要',
          icon: Clock
        };
      default:
        return {
          bgClass: 'normal-bg',
          borderClass: 'border-blue-500',
          textClass: 'text-blue-700',
          iconClass: 'text-blue-600',
          label: '一般',
          icon: Activity
        };
    }
  };

  const getActionConfig = (type: string) => {
    switch (type) {
      case 'medication':
        return { icon: Pill, label: '用药调整', color: 'text-purple-600' };
      case 'examination':
        return { icon: Activity, label: '检查预约', color: 'text-green-600' };
      case 'lifestyle':
        return { icon: MessageSquare, label: '生活指导', color: 'text-blue-600' };
      case 'followup':
        return { icon: Calendar, label: '随访安排', color: 'text-orange-600' };
      default:
        return { icon: Activity, label: '其他', color: 'text-gray-600' };
    }
  };

  const urgencyConfig = getUrgencyConfig(data.urgencyLevel);
  const actionConfig = getActionConfig(data.actionType);
  const UrgencyIcon = urgencyConfig.icon;
  const ActionIcon = actionConfig.icon;

  const handleConfirm = async () => {
    if (!selectedAction) return;
    
    setIsProcessing(true);
    
    // 模拟处理时间
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    onConfirm(selectedAction, customNote);
    setIsProcessing(false);
    setSelectedAction('');
    setCustomNote('');
  };

  const actionOptions = [
    data.aiSuggestion,
    ...(data.alternativeOptions || [])
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
        {/* 头部 */}
        <div className={`p-4 ${urgencyConfig.bgClass} border-b ${urgencyConfig.borderClass} rounded-t-xl`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <UrgencyIcon size={20} className={urgencyConfig.iconClass} />
              <div>
                <h2 className={`font-bold ${urgencyConfig.textClass}`}>
                  患者干预确认 - {urgencyConfig.label}
                </h2>
                <p className={`text-sm ${urgencyConfig.textClass} opacity-80`}>
                  需要您的医疗决策确认
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className={`p-1 rounded-full hover:bg-black/10 ${urgencyConfig.textClass}`}
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* 患者信息 */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
              {data.patientAvatar ? (
                <img src={data.patientAvatar} alt="患者头像" className="w-12 h-12 rounded-full" />
              ) : (
                <User size={20} className="text-gray-500" />
              )}
            </div>
            <div>
              <h3 className="font-semibold text-gray-800">{data.patientName}</h3>
              <p className="text-sm text-gray-600">{data.patientAge}岁 · {data.patientDiagnosis}</p>
            </div>
            <div className="ml-auto flex items-center space-x-2">
              <ActionIcon size={16} className={actionConfig.color} />
              <span className={`text-sm font-medium ${actionConfig.color}`}>
                {actionConfig.label}
              </span>
            </div>
          </div>
        </div>

        {/* 异常详情 */}
        <div className="p-4 border-b border-gray-200">
          <h4 className="font-medium text-gray-800 mb-2">📊 异常详情</h4>
          <div className="bg-gray-50 rounded-lg p-3 space-y-2">
            <p className="text-sm text-gray-700">{data.anomalyDetails}</p>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">当前值:</span>
              <span className="font-medium text-red-600">{data.currentValue}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">正常范围:</span>
              <span className="font-medium text-green-600">{data.normalRange}</span>
            </div>
          </div>
        </div>

        {/* AI建议方案 */}
        <div className="p-4 border-b border-gray-200">
          <h4 className="font-medium text-gray-800 mb-3">🤖 AI智能建议</h4>
          <div className="space-y-2">
            {actionOptions.map((option, index) => (
              <label
                key={index}
                className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
              >
                <input
                  type="radio"
                  name="action"
                  value={option}
                  checked={selectedAction === option}
                  onChange={(e) => setSelectedAction(e.target.value)}
                  className="mt-1 text-primary focus:ring-primary"
                />
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-800">{option}</p>
                  {index === 0 && (
                    <p className="text-xs text-primary mt-1">💡 AI推荐方案</p>
                  )}
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* 自定义备注 */}
        <div className="p-4 border-b border-gray-200">
          <h4 className="font-medium text-gray-800 mb-2">📝 医嘱备注（可选）</h4>
          <textarea
            value={customNote}
            onChange={(e) => setCustomNote(e.target.value)}
            placeholder="输入特殊说明或注意事项..."
            className="w-full p-3 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-primary focus:border-primary outline-none resize-none"
            rows={3}
          />
        </div>

        {/* 底部按钮 */}
        <div className="p-4 flex space-x-3">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
          >
            取消
          </button>
          <button
            onClick={handleConfirm}
            disabled={!selectedAction || isProcessing}
            className="flex-1 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-600 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2"></div>
                执行中...
              </>
            ) : (
              <>
                <CheckCircle size={16} className="mr-2" />
                确认执行
              </>
            )}
          </button>
        </div>

        {/* 执行后的反馈 */}
        {isProcessing && (
          <div className="p-4 bg-blue-50 border-t border-blue-200">
            <div className="flex items-center space-x-2 text-blue-700">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-700 border-t-transparent"></div>
              <span className="text-sm">正在同步到患者端华医通app...</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ConfirmationModal;
