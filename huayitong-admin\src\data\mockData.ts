// 患者数据
export interface Patient {
  id: string;
  name: string;
  age: number;
  gender: 'male' | 'female';
  diagnosis: string;
  riskLevel: 'high' | 'medium' | 'low';
  avatar?: string;
  phone: string;
  lastVisit: string;
  nextAppointment?: string;
  vitals: {
    heartRate: number;
    bloodPressure: string;
    temperature: number;
    bloodSugar: number;
  };
  medications: string[];
  alerts: Array<{
    type: 'urgent' | 'important' | 'normal';
    message: string;
    timestamp: string;
  }>;
}

// 排班数据
export interface Appointment {
  id: string;
  patientId: string;
  patientName: string;
  time: string;
  duration: number;
  type: 'consultation' | 'followup' | 'emergency';
  status: 'scheduled' | 'completed' | 'cancelled';
  notes?: string;
}

// 任务数据
export interface Task {
  id: string;
  patientId: string;
  patientName: string;
  type: 'medication' | 'examination' | 'followup' | 'lifestyle';
  title: string;
  description: string;
  urgency: 'high' | 'medium' | 'low';
  status: 'pending' | 'completed' | 'overdue';
  dueDate: string;
  createdAt: string;
}

// 课程数据
export interface Course {
  id: string;
  title: string;
  instructor: string;
  duration: string;
  category: string;
  level: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  enrollment: number;
  thumbnail: string;
  description: string;
  isLive: boolean;
  startTime?: string;
}

// 指南数据
export interface Guideline {
  id: string;
  title: string;
  organization: string;
  version: string;
  publishDate: string;
  category: string;
  evidenceLevel: 'A' | 'B' | 'C';
  summary: string;
  keyPoints: string[];
  downloadUrl: string;
}

// 文献数据
export interface Literature {
  id: string;
  title: string;
  authors: string[];
  journal: string;
  publishDate: string;
  impact: number;
  abstract: string;
  keywords: string[];
  pdfUrl: string;
  citationCount: number;
}

// 模拟患者数据
export const mockPatients: Patient[] = [
  {
    id: '1',
    name: '张三',
    age: 65,
    gender: 'male',
    diagnosis: '高血压、冠心病',
    riskLevel: 'high',
    phone: '138****1234',
    lastVisit: '2025-06-15',
    nextAppointment: '2025-06-21 09:30',
    vitals: {
      heartRate: 82,
      bloodPressure: '160/95',
      temperature: 36.7,
      bloodSugar: 6.8
    },
    medications: ['阿司匹林', '美托洛尔', '氨氯地平'],
    alerts: [
      {
        type: 'urgent',
        message: '血压持续偏高，需调整用药',
        timestamp: '2025-06-20 08:30'
      }
    ]
  },
  {
    id: '2',
    name: '李四',
    age: 58,
    gender: 'female',
    diagnosis: '糖尿病、高血脂',
    riskLevel: 'medium',
    phone: '139****5678',
    lastVisit: '2025-06-10',
    nextAppointment: '2025-06-22 14:00',
    vitals: {
      heartRate: 75,
      bloodPressure: '135/85',
      temperature: 36.4,
      bloodSugar: 8.2
    },
    medications: ['二甲双胍', '格列齐特', '阿托伐他汀'],
    alerts: [
      {
        type: 'important',
        message: '血糖控制欠佳，建议调整方案',
        timestamp: '2025-06-19 16:20'
      }
    ]
  },
  {
    id: '3',
    name: '王五',
    age: 72,
    gender: 'male',
    diagnosis: '心房颤动、心衰',
    riskLevel: 'high',
    phone: '137****9012',
    lastVisit: '2025-06-12',
    nextAppointment: '2025-06-23 10:00',
    vitals: {
      heartRate: 110,
      bloodPressure: '120/70',
      temperature: 36.5,
      bloodSugar: 5.8
    },
    medications: ['华法林', '地高辛', '呋塞米'],
    alerts: [
      {
        type: 'urgent',
        message: '心率不齐，需要密切监测',
        timestamp: '2025-06-20 06:45'
      }
    ]
  },
  {
    id: '4',
    name: '赵六',
    age: 45,
    gender: 'female',
    diagnosis: '甲状腺功能亢进',
    riskLevel: 'low',
    phone: '136****3456',
    lastVisit: '2025-06-08',
    nextAppointment: '2025-06-25 15:30',
    vitals: {
      heartRate: 95,
      bloodPressure: '125/80',
      temperature: 36.8,
      bloodSugar: 5.2
    },
    medications: ['甲巯咪唑', '普萘洛尔'],
    alerts: []
  },
  {
    id: '5',
    name: '孙七',
    age: 68,
    gender: 'male',
    diagnosis: '慢性肾病、贫血',
    riskLevel: 'medium',
    phone: '135****7890',
    lastVisit: '2025-06-14',
    nextAppointment: '2025-06-24 11:00',
    vitals: {
      heartRate: 88,
      bloodPressure: '140/90',
      temperature: 36.3,
      bloodSugar: 6.1
    },
    medications: ['厄贝沙坦', '碳酸钙', '促红素'],
    alerts: [
      {
        type: 'normal',
        message: '肾功能稳定，继续观察',
        timestamp: '2025-06-19 14:15'
      }
    ]
  }
];

// 模拟排班数据
export const mockAppointments: Appointment[] = [
  {
    id: '1',
    patientId: '1',
    patientName: '张三',
    time: '2025-06-21 09:30',
    duration: 30,
    type: 'consultation',
    status: 'scheduled',
    notes: '血压复查，调整用药方案'
  },
  {
    id: '2',
    patientId: '2',
    patientName: '李四',
    time: '2025-06-21 10:00',
    duration: 20,
    type: 'followup',
    status: 'scheduled',
    notes: '糖尿病随访，查看血糖记录'
  },
  {
    id: '3',
    patientId: '3',
    patientName: '王五',
    time: '2025-06-21 10:30',
    duration: 45,
    type: 'consultation',
    status: 'scheduled',
    notes: '心衰评估，心电图检查'
  },
  {
    id: '4',
    patientId: '4',
    patientName: '赵六',
    time: '2025-06-21 11:30',
    duration: 20,
    type: 'followup',
    status: 'scheduled',
    notes: '甲状腺功能复查'
  },
  {
    id: '5',
    patientId: '5',
    patientName: '孙七',
    time: '2025-06-21 14:00',
    duration: 30,
    type: 'consultation',
    status: 'scheduled',
    notes: '肾功能评估，调整用药'
  }
];

// 模拟任务数据
export const mockTasks: Task[] = [
  {
    id: '1',
    patientId: '1',
    patientName: '张三',
    type: 'medication',
    title: '调整降压药剂量',
    description: '患者血压160/95，建议增加氨氯地平剂量至10mg qd',
    urgency: 'high',
    status: 'pending',
    dueDate: '2025-06-21',
    createdAt: '2025-06-20 08:30'
  },
  {
    id: '2',
    patientId: '2',
    patientName: '李四',
    type: 'examination',
    title: '预约糖化血红蛋白检查',
    description: '血糖控制欠佳，需要评估近3个月血糖控制情况',
    urgency: 'medium',
    status: 'pending',
    dueDate: '2025-06-22',
    createdAt: '2025-06-19 16:20'
  },
  {
    id: '3',
    patientId: '3',
    patientName: '王五',
    type: 'followup',
    title: '心电图随访',
    description: '心房颤动患者，需要定期监测心律情况',
    urgency: 'high',
    status: 'pending',
    dueDate: '2025-06-21',
    createdAt: '2025-06-20 06:45'
  },
  {
    id: '4',
    patientId: '4',
    patientName: '赵六',
    type: 'lifestyle',
    title: '饮食指导',
    description: '甲亢患者，建议避免碘摄入过多，禁食海带等高碘食物',
    urgency: 'low',
    status: 'completed',
    dueDate: '2025-06-20',
    createdAt: '2025-06-18 10:15'
  },
  {
    id: '5',
    patientId: '5',
    patientName: '孙七',
    type: 'examination',
    title: '肾功能复查',
    description: '慢性肾病患者，定期监测肌酐和尿素氮水平',
    urgency: 'medium',
    status: 'pending',
    dueDate: '2025-06-24',
    createdAt: '2025-06-19 14:15'
  }
];

// 模拟课程数据
export const mockCourses: Course[] = [
  {
    id: '1',
    title: '急性心肌梗死的诊断与治疗',
    instructor: '李主任',
    duration: '45分钟',
    category: '心血管',
    level: 'intermediate',
    rating: 4.8,
    enrollment: 1247,
    thumbnail: '/images/course1.jpg',
    description: '详细讲解STEMI和NSTEMI的诊断标准、治疗原则和预后评估',
    isLive: false
  },
  {
    id: '2',
    title: '糖尿病并发症防治策略',
    instructor: '王教授',
    duration: '60分钟',
    category: '内分泌',
    level: 'advanced',
    rating: 4.9,
    enrollment: 892,
    thumbnail: '/images/course2.jpg',
    description: '糖尿病微血管和大血管并发症的早期识别和干预措施',
    isLive: true,
    startTime: '2025-06-21 19:00'
  },
  {
    id: '3',
    title: '高血压规范化管理',
    instructor: '张医生',
    duration: '30分钟',
    category: '心血管',
    level: 'beginner',
    rating: 4.7,
    enrollment: 2156,
    thumbnail: '/images/course3.jpg',
    description: '高血压的分级诊断、危险分层和个体化治疗方案制定',
    isLive: false
  }
];

// 模拟指南数据
export const mockGuidelines: Guideline[] = [
  {
    id: '1',
    title: '中国高血压防治指南（2023年修订版）',
    organization: '中华医学会心血管病学分会',
    version: '2023版',
    publishDate: '2023-11-15',
    category: '心血管',
    evidenceLevel: 'A',
    summary: '更新了血压测量标准、诊断标准和治疗目标，强调个体化治疗方案',
    keyPoints: [
      '血压分级标准调整',
      '家庭血压监测重要性',
      '联合用药策略优化',
      '特殊人群管理'
    ],
    downloadUrl: '/guidelines/hypertension-2023.pdf'
  },
  {
    id: '2',
    title: '急性ST段抬高型心肌梗死诊疗指南',
    organization: '中华医学会心血管病学分会',
    version: '2023版',
    publishDate: '2023-08-20',
    category: '心血管',
    evidenceLevel: 'A',
    summary: '规范STEMI患者的急诊处理流程，优化再灌注治疗策略',
    keyPoints: [
      'D2B时间<90分钟',
      '抗栓治疗方案',
      '机械循环支持',
      '并发症处理'
    ],
    downloadUrl: '/guidelines/stemi-2023.pdf'
  },
  {
    id: '3',
    title: '2型糖尿病防治指南',
    organization: '中华医学会糖尿病学分会',
    version: '2023版',
    publishDate: '2023-09-10',
    category: '内分泌',
    evidenceLevel: 'A',
    summary: '更新了血糖控制目标、药物选择和并发症筛查标准',
    keyPoints: [
      'HbA1c目标个体化',
      '新型降糖药物应用',
      '心血管风险评估',
      '综合管理策略'
    ],
    downloadUrl: '/guidelines/diabetes-2023.pdf'
  }
];

// 模拟文献数据
export const mockLiterature: Literature[] = [
  {
    id: '1',
    title: 'Cardiovascular outcomes with sodium-glucose cotransporter-2 inhibitors vs other glucose-lowering drugs in type 2 diabetes',
    authors: ['Smith J.', 'Johnson M.', 'Williams R.'],
    journal: 'Circulation',
    publishDate: '2023-12-15',
    impact: 8.5,
    abstract: '本研究比较了SGLT2抑制剂与其他降糖药物在2型糖尿病患者中的心血管结局差异...',
    keywords: ['SGLT2抑制剂', '心血管结局', '2型糖尿病'],
    pdfUrl: '/literature/sglt2-outcomes.pdf',
    citationCount: 42
  },
  {
    id: '2',
    title: 'AI-powered early detection of acute coronary syndrome in emergency departments',
    authors: ['Chen L.', 'Zhang W.', 'Liu H.'],
    journal: 'Nature Medicine',
    publishDate: '2024-01-08',
    impact: 12.3,
    abstract: '人工智能在急诊科早期识别急性冠脉综合征的应用研究，显著提高诊断准确性...',
    keywords: ['人工智能', '急性冠脉综合征', '早期诊断'],
    pdfUrl: '/literature/ai-acs-detection.pdf',
    citationCount: 18
  },
  {
    id: '3',
    title: 'Remote monitoring of heart failure patients using wearable devices: a systematic review',
    authors: ['Brown K.', 'Davis P.', 'Miller S.'],
    journal: 'Journal of the American College of Cardiology',
    publishDate: '2023-11-22',
    impact: 9.8,
    abstract: '可穿戴设备在心衰患者远程监测中的应用系统性综述，评估了监测效果和临床价值...',
    keywords: ['可穿戴设备', '心衰', '远程监测'],
    pdfUrl: '/literature/wearable-hf-monitoring.pdf',
    citationCount: 35
  }
];

// 统计数据
export const mockStats = {
  todayPatients: 8,
  urgentAlerts: 3,
  pendingTasks: 12,
  completedTasks: 15,
  weeklyAppointments: 45,
  patientSatisfaction: 4.8,
  responseTime: '2.3分钟',
  emergencyRate: '5.2%'
};

// 确认数据示例
export const mockConfirmationData = {
  id: 'conf-001',
  patientName: '张三',
  patientAge: 65,
  patientDiagnosis: '高血压、冠心病',
  urgencyLevel: 'urgent' as const,
  actionType: 'medication' as const,
  anomalyDetails: '患者血压持续偏高，收缩压160mmHg，舒张压95mmHg，超出目标范围',
  currentValue: '160/95 mmHg',
  normalRange: '<140/90 mmHg',
  aiSuggestion: '建议增加氨氯地平剂量至10mg每日一次',
  alternativeOptions: [
    '调整美托洛尔剂量至100mg每日两次',
    '加用氢氯噻嗪12.5mg每日一次',
    '预约心血管专科进一步评估'
  ]
};
