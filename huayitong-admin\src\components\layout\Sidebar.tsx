import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { 
  Calendar,
  Users,
  FileText,
  GraduationCap,
  BookOpen,
  Microscope,
  BarChart3,
  Stethoscope
} from 'lucide-react';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const menuItems = [
    {
      path: '/dashboard',
      icon: BarChart3,
      label: '工作台',
      description: '总览面板'
    },
    {
      path: '/calendar',
      icon: Calendar,
      label: '出诊日历',
      description: '排班管理'
    },
    {
      path: '/patients',
      icon: Users,
      label: '患者管理',
      description: '档案监测'
    },
    {
      path: '/tasks',
      icon: FileText,
      label: '任务记录',
      description: '医嘱管理'
    },
    {
      path: '/education',
      icon: GraduationCap,
      label: '临床课堂',
      description: '医学教育'
    },
    {
      path: '/guidelines',
      icon: BookOpen,
      label: '指南共识',
      description: '诊疗指南'
    },
    {
      path: '/research',
      icon: Microscope,
      label: '学术前沿',
      description: '文献会议'
    },
    {
      path: '/analytics',
      icon: BarChart3,
      label: '科研助手',
      description: '数据分析'
    }
  ];

  const handleNavigate = (path: string) => {
    navigate(path);
  };

  return (
    <div className="sidebar flex flex-col h-full">
      {/* 顶部标题 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 rounded-lg overflow-hidden">
            <img 
              src="/logo.jpeg" 
              alt="系统Logo" 
              className="w-full h-full object-cover"
            />
          </div>
          <div>
            <h1 className="font-bold text-gray-800">全域专病管理智能体</h1>
            <p className="text-xs text-gray-500">管理端系统</p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-2 space-y-1">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.path || 
            (item.path === '/dashboard' && location.pathname === '/');
          
          return (
            <button
              key={item.path}
              onClick={() => handleNavigate(item.path)}
              className={`w-full flex items-center px-3 py-2.5 rounded-lg text-left transition-all duration-200 group ${
                isActive 
                  ? 'bg-primary text-white shadow-sm' 
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-800'
              }`}
            >
              <Icon 
                size={18} 
                className={`mr-3 flex-shrink-0 ${
                  isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'
                }`} 
              />
              <div className="flex-1 min-w-0">
                <p className={`text-sm font-medium truncate ${
                  isActive ? 'text-white' : 'text-gray-700'
                }`}>
                  {item.label}
                </p>
                <p className={`text-xs truncate ${
                  isActive ? 'text-primary-100' : 'text-gray-500'
                }`}>
                  {item.description}
                </p>
              </div>
            </button>
          );
        })}
      </nav>

      {/* 底部用户信息 */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-gray-600">李</span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-800 truncate">李教授</p>
            <p className="text-xs text-gray-500 truncate">心内科主任</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
