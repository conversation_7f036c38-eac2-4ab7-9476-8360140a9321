import React, { useState } from 'react';
import { 
  Search, 
  Filter,
  User,
  Heart,
  Activity,
  Thermometer,
  Droplets,
  AlertTriangle,
  Phone,
  Calendar,
  MapPin,
  TrendingUp,
  ChevronRight,
  Eye,
  Clock
} from 'lucide-react';
import { mockPatients, Patient } from '../data/mockData';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const PatientsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRisk, setSelectedRisk] = useState<string>('all');
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);

  const filteredPatients = mockPatients.filter(patient => {
    const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         patient.diagnosis.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRisk = selectedRisk === 'all' || patient.riskLevel === selectedRisk;
    return matchesSearch && matchesRisk;
  });

  const riskLevels = [
    { value: 'all', label: '全部', count: mockPatients.length },
    { value: 'high', label: '高风险', count: mockPatients.filter(p => p.riskLevel === 'high').length },
    { value: 'medium', label: '中风险', count: mockPatients.filter(p => p.riskLevel === 'medium').length },
    { value: 'low', label: '低风险', count: mockPatients.filter(p => p.riskLevel === 'low').length }
  ];

  const getRiskConfig = (level: string) => {
    switch (level) {
      case 'high':
        return { color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200', label: '高风险' };
      case 'medium':
        return { color: 'text-yellow-600', bg: 'bg-yellow-50', border: 'border-yellow-200', label: '中风险' };
      case 'low':
        return { color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200', label: '低风险' };
      default:
        return { color: 'text-gray-600', bg: 'bg-gray-50', border: 'border-gray-200', label: '未知' };
    }
  };

  // 模拟趋势数据
  const getTrendData = (patient: Patient) => [
    { date: '6/15', bp_sys: 158, bp_dia: 92, hr: 85 },
    { date: '6/16', bp_sys: 162, bp_dia: 95, hr: 88 },
    { date: '6/17', bp_sys: 155, bp_dia: 89, hr: 82 },
    { date: '6/18', bp_sys: 160, bp_dia: 94, hr: 84 },
    { date: '6/19', bp_sys: 165, bp_dia: 98, hr: 90 },
    { date: '6/20', bp_sys: parseInt(patient.vitals.bloodPressure.split('/')[0]), 
             bp_dia: parseInt(patient.vitals.bloodPressure.split('/')[1]), 
             hr: patient.vitals.heartRate }
  ];

  return (
    <div className="h-full flex">
      {/* 左侧患者列表 */}
      <div className="w-96 border-r border-gray-200 bg-white">
        {/* 顶部搜索和筛选 */}
        <div className="p-4 border-b border-gray-200">
          <h1 className="text-xl font-bold text-gray-800 mb-4">患者管理</h1>
          
          {/* 搜索框 */}
          <div className="relative mb-4">
            <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="搜索患者姓名或诊断"
              className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary outline-none"
            />
          </div>

          {/* 风险筛选 */}
          <div className="flex space-x-2">
            {riskLevels.map((risk) => (
              <button
                key={risk.value}
                onClick={() => setSelectedRisk(risk.value)}
                className={`px-3 py-1.5 text-xs rounded-full border transition-colors ${
                  selectedRisk === risk.value
                    ? 'bg-primary text-white border-primary'
                    : 'bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100'
                }`}
              >
                {risk.label} ({risk.count})
              </button>
            ))}
          </div>
        </div>

        {/* 患者列表 */}
        <div className="overflow-y-auto" style={{ height: 'calc(100% - 140px)' }}>
          {filteredPatients.map((patient) => {
            const riskConfig = getRiskConfig(patient.riskLevel);
            const hasUrgentAlerts = patient.alerts.some(alert => alert.type === 'urgent');
            
            return (
              <div
                key={patient.id}
                onClick={() => setSelectedPatient(patient)}
                className={`patient-card m-3 cursor-pointer transition-all ${
                  selectedPatient?.id === patient.id ? 'ring-2 ring-primary' : ''
                } ${hasUrgentAlerts ? 'border-red-300 bg-red-50' : ''}`}
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-gray-600">
                        {patient.name[0]}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">{patient.name}</h3>
                      <p className="text-sm text-gray-600">{patient.age}岁 · {patient.gender === 'male' ? '男' : '女'}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <span className={`text-xs px-2 py-1 rounded-full ${riskConfig.bg} ${riskConfig.color} border ${riskConfig.border}`}>
                      {riskConfig.label}
                    </span>
                    {hasUrgentAlerts && (
                      <div className="flex items-center mt-1">
                        <AlertTriangle size={12} className="text-red-500 mr-1" />
                        <span className="text-xs text-red-600">紧急</span>
                      </div>
                    )}
                  </div>
                </div>

                <p className="text-sm text-gray-700 mb-3">{patient.diagnosis}</p>

                <div className="grid grid-cols-2 gap-2 text-xs">
                  <div className="flex items-center">
                    <Heart size={12} className="text-red-500 mr-1" />
                    <span>{patient.vitals.heartRate} bpm</span>
                  </div>
                  <div className="flex items-center">
                    <Activity size={12} className="text-blue-500 mr-1" />
                    <span>{patient.vitals.bloodPressure}</span>
                  </div>
                </div>

                {patient.alerts.length > 0 && (
                  <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
                    <p className="text-yellow-800">{patient.alerts[0].message}</p>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* 右侧患者详情 */}
      <div className="flex-1 bg-gray-50">
        {selectedPatient ? (
          <div className="p-6 h-full overflow-y-auto">
            {/* 患者基本信息 */}
            <div className="glass-card rounded-lg p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                    <span className="text-xl font-medium text-gray-600">
                      {selectedPatient.name[0]}
                    </span>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-800">{selectedPatient.name}</h1>
                    <p className="text-gray-600">{selectedPatient.age}岁 · {selectedPatient.gender === 'male' ? '男性' : '女性'}</p>
                    <p className="text-sm text-gray-500">{selectedPatient.diagnosis}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskConfig(selectedPatient.riskLevel).bg} ${getRiskConfig(selectedPatient.riskLevel).color} border ${getRiskConfig(selectedPatient.riskLevel).border}`}>
                    {getRiskConfig(selectedPatient.riskLevel).label}
                  </span>
                  <div className="flex items-center mt-2 text-sm text-gray-500">
                    <Phone size={14} className="mr-1" />
                    {selectedPatient.phone}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="flex items-center">
                  <Calendar size={16} className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">上次就诊</p>
                    <p className="font-medium text-gray-800">{selectedPatient.lastVisit}</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <Clock size={16} className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">下次预约</p>
                    <p className="font-medium text-gray-800">
                      {selectedPatient.nextAppointment ? 
                        new Date(selectedPatient.nextAppointment).toLocaleDateString('zh-CN') : 
                        '未安排'
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center">
                  <MapPin size={16} className="text-gray-400 mr-2" />
                  <div>
                    <p className="text-xs text-gray-500">科室</p>
                    <p className="font-medium text-gray-800">心内科</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 实时生理指征 */}
              <div className="glass-card rounded-lg p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <Activity size={20} className="text-primary mr-2" />
                  实时生理指征
                </h2>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <Heart size={18} className="text-red-500" />
                      <TrendingUp size={14} className="text-green-500" />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">心率</p>
                    <p className="text-xl font-bold text-gray-800">{selectedPatient.vitals.heartRate}</p>
                    <p className="text-xs text-gray-500">bpm</p>
                  </div>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <Activity size={18} className="text-blue-500" />
                      <TrendingUp size={14} className="text-red-500" />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">血压</p>
                    <p className="text-xl font-bold text-gray-800">{selectedPatient.vitals.bloodPressure}</p>
                    <p className="text-xs text-gray-500">mmHg</p>
                  </div>
                  
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <Thermometer size={18} className="text-yellow-500" />
                      <span className="text-xs text-green-600">正常</span>
                    </div>
                    <p className="text-sm text-gray-600 mt-2">体温</p>
                    <p className="text-xl font-bold text-gray-800">{selectedPatient.vitals.temperature}</p>
                    <p className="text-xs text-gray-500">°C</p>
                  </div>
                  
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <Droplets size={18} className="text-purple-500" />
                      <TrendingUp size={14} className="text-yellow-500" />
                    </div>
                    <p className="text-sm text-gray-600 mt-2">血糖</p>
                    <p className="text-xl font-bold text-gray-800">{selectedPatient.vitals.bloodSugar}</p>
                    <p className="text-xs text-gray-500">mmol/L</p>
                  </div>
                </div>
              </div>

              {/* 用药情况 */}
              <div className="glass-card rounded-lg p-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4">当前用药</h2>
                <div className="space-y-3">
                  {selectedPatient.medications.map((med, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                          <Droplets size={14} className="text-purple-600" />
                        </div>
                        <span className="font-medium text-gray-800">{med}</span>
                      </div>
                      <span className="text-sm text-green-600">按时服用</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 健康趋势图表 */}
            <div className="glass-card rounded-lg p-6 mt-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">健康趋势分析</h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={getTrendData(selectedPatient)}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="date" 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#888' }}
                    />
                    <YAxis 
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12, fill: '#888' }}
                    />
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'white', 
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        fontSize: '12px'
                      }}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="bp_sys" 
                      stroke="#00B4A6" 
                      strokeWidth={2}
                      dot={{ fill: '#00B4A6', strokeWidth: 2, r: 4 }}
                      name="收缩压"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="bp_dia" 
                      stroke="#3B82F6" 
                      strokeWidth={2}
                      dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                      name="舒张压"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="hr" 
                      stroke="#EF4444" 
                      strokeWidth={2}
                      dot={{ fill: '#EF4444', strokeWidth: 2, r: 4 }}
                      name="心率"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* 报警和提醒 */}
            {selectedPatient.alerts.length > 0 && (
              <div className="glass-card rounded-lg p-6 mt-6">
                <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <AlertTriangle size={20} className="text-red-600 mr-2" />
                  健康提醒
                </h2>
                <div className="space-y-3">
                  {selectedPatient.alerts.map((alert, index) => (
                    <div 
                      key={index}
                      className={`p-4 rounded-lg border ${
                        alert.type === 'urgent' ? 'bg-red-50 border-red-200' :
                        alert.type === 'important' ? 'bg-yellow-50 border-yellow-200' :
                        'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <p className={`font-medium ${
                          alert.type === 'urgent' ? 'text-red-800' :
                          alert.type === 'important' ? 'text-yellow-800' :
                          'text-blue-800'
                        }`}>
                          {alert.message}
                        </p>
                        <span className={`text-xs ${
                          alert.type === 'urgent' ? 'text-red-600' :
                          alert.type === 'important' ? 'text-yellow-600' :
                          'text-blue-600'
                        }`}>
                          {alert.timestamp}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <Eye size={48} className="text-gray-300 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-500 mb-2">选择患者查看详情</h2>
              <p className="text-gray-400">在左侧列表中点击患者姓名查看详细信息</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PatientsPage;
