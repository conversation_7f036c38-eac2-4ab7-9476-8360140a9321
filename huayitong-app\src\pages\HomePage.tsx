import React, { useState, useRef, useEffect } from 'react';
import { 
  Settings, 
  Video, 
  Mic, 
  Send, 
  Calendar,
  Stethoscope,
  Pill,
  MapPin
} from 'lucide-react';

interface Message {
  id: string;
  text: string;
  isAI: boolean;
  timestamp: Date;
}

const HomePage: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: '您好！我是华医通智能助手，今天感觉怎么样？我可以帮您挂号、查询、导诊或解答健康问题。',
      isAI: true,
      timestamp: new Date()
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const quickActions = [
    { icon: Calendar, label: '挂号', color: 'bg-blue-50 text-blue-600' },
    { icon: Stethoscope, label: '检查', color: 'bg-green-50 text-green-600' },
    { icon: Pill, label: '用药', color: 'bg-purple-50 text-purple-600' },
    { icon: MapPin, label: '导诊', color: 'bg-orange-50 text-orange-600' }
  ];

  const handleSendMessage = (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: messageText,
      isAI: false,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');

    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = generateAIResponse(messageText);
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: aiResponse,
        isAI: true,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, aiMessage]);
    }, 1000);
  };

  const generateAIResponse = (userMessage: string): string => {
    const lowerMessage = userMessage.toLowerCase();
    
    if (lowerMessage.includes('挂号') || lowerMessage.includes('预约')) {
      return '好的，我帮您查看可预约的科室。请问您需要挂哪个科室的号？比如心内科、消化科、骨科等。我会为您推荐最合适的专家。';
    } else if (lowerMessage.includes('检查') || lowerMessage.includes('报告')) {
      return '我可以帮您查看检查报告或预约检查项目。您的最近一次检查报告显示各项指标正常。如需预约新的检查，请告诉我具体项目。';
    } else if (lowerMessage.includes('用药') || lowerMessage.includes('药物')) {
      return '关于用药问题，请遵医嘱服用。您当前的用药计划：高血压药物每日早晨饭后服用。如有不适，请及时联系医生调整。';
    } else if (lowerMessage.includes('导诊') || lowerMessage.includes('路线')) {
      return '华西医院导诊服务：门诊大楼1楼是挂号和缴费区，各科室分布在2-6楼。我可以为您规划最佳就诊路线，避免迷路和等待。';
    } else if (lowerMessage.includes('视频') || lowerMessage.includes('通话')) {
      return '正在为您连接视频咨询服务，专家医生将在2-3分钟内接入。请确保您的网络环境良好，并准备好相关病历资料。';
    } else {
      return '我理解您的问题。作为您的专属健康助手，我会根据您的健康档案提供个性化建议。如需详细咨询，建议您预约专家面诊或视频咨询。';
    }
  };

  const handleQuickAction = (label: string) => {
    handleSendMessage(`我想${label}`);
  };

  const handleVoiceInput = () => {
    setIsRecording(!isRecording);
    // 这里实现语音输入逻辑
    if (!isRecording) {
      // 模拟语音输入
      setTimeout(() => {
        setIsRecording(false);
        handleSendMessage('我想挂明天的心内科号');
      }, 3000);
    }
  };

  return (
    <div className="mobile-container gradient-bg">
      {/* 顶部导航 */}
      <header className="flex items-center justify-between p-4 bg-white/90 backdrop-blur-sm border-b border-white/20">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
            <span className="text-white text-lg">👩‍⚕️</span>
          </div>
          <div>
            <h1 className="font-semibold text-gray-800">华医通AI助手</h1>
            <p className="text-sm text-gray-500">智能医疗服务</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button className="p-2 rounded-full bg-gray-100 text-gray-600">
            <Settings size={20} />
          </button>
          <button className="p-2 rounded-full bg-primary text-white">
            <Video size={20} />
          </button>
        </div>
      </header>

      {/* 对话区域 */}
      <div className="flex-1 px-4 py-6 overflow-y-auto" style={{ paddingBottom: '200px' }}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.isAI ? 'justify-start' : 'justify-end'}`}
            >
              <div
                className={`chat-bubble ${
                  message.isAI ? 'chat-bubble-ai' : 'chat-bubble-user'
                }`}
              >
                <p className="text-sm">{message.text}</p>
                <span className="text-xs opacity-70 mt-1 block">
                  {message.timestamp.toLocaleTimeString('zh-CN', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}
                </span>
              </div>
            </div>
          ))}
        </div>
        <div ref={messagesEndRef} />
      </div>

      {/* 快捷操作 */}
      <div className="absolute bottom-32 left-0 right-0 px-4">
        <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/20">
          <p className="text-sm text-gray-600 mb-3 font-medium">🎯 快捷操作</p>
          <div className="grid grid-cols-4 gap-3">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <button
                  key={index}
                  onClick={() => handleQuickAction(action.label)}
                  className={`flex flex-col items-center p-3 rounded-xl ${action.color} transition-transform active:scale-95`}
                >
                  <Icon size={18} />
                  <span className="text-xs mt-1 font-medium">{action.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </div>

      {/* 输入区域 */}
      <div className="absolute bottom-16 left-0 right-0 p-4">
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <div className="flex items-center p-3 space-x-3">
            <button
              onClick={handleVoiceInput}
              className={`p-3 rounded-full transition-colors ${
                isRecording 
                  ? 'bg-red-500 text-white animate-pulse' 
                  : 'bg-primary text-white'
              }`}
            >
              <Mic size={18} />
            </button>
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder={isRecording ? '正在录音...' : '有什么健康问题我都可以帮您'}
              className="flex-1 px-4 py-2 bg-gray-50 rounded-full border-none outline-none text-sm"
              disabled={isRecording}
            />
            <button
              onClick={() => handleSendMessage()}
              disabled={!inputText.trim() || isRecording}
              className="p-3 bg-primary text-white rounded-full disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Send size={18} />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomePage;
